/**
 * Articles Page Styling
 * Based on Figma design for the articles listing page
 */

/* Fluid CSS Variables for Article Grid */
:root {
    /* Spacing variables using clamp() for fluid scaling */
    --article-gap-row: clamp(2rem, 3vw, 5rem);     /* Row gap between articles */
    --article-gap-col: clamp(1.5rem, 2vw, 3rem);   /* Column gap between articles */
    --article-padding: clamp(1.5rem, 5vw, 3rem);   /* Horizontal padding for article grid */
    
    /* Typography scaling */
    --article-title-size: clamp(1.4rem, 1.2vw, 1.8rem);     /* Article card title */
    --article-excerpt-size: clamp(1rem, 0.8vw, 1.2rem);     /* Article card excerpt */
    --article-meta-size: clamp(0.8rem, 0.6vw, 1rem);        /* Article meta text */
    
    /* Card element spacing */
    --article-card-gap: clamp(0.8rem, 0.8vw, 1.5rem);       /* Gap between card elements */
    --article-meta-gap: clamp(0.5rem, 0.5vw, 0.8rem);       /* Gap between meta items */
    --article-content-padding: clamp(0rem, 1vw, 1.5rem);    /* Right padding for content */
}

/* Article Header Section */
.articles-header {
    background-color: #D84406; /* Default color - will be overridden by category colors in Ghost CMS */
    border-radius: 8px;
    margin: 0.5rem;
    margin-bottom: 2.5rem;
    overflow: hidden;
    position: relative;
}

.articles-header-wrapper {
    width: 100%;
    padding: 0 50px; /* Exact padding from Figma */
    display: flex;
    flex-direction: column;
    background: none; /* Override any background image */
    background-size: unset;
    background-position: unset;
    position: relative;
    height: auto; /* Override height constraints */
    min-height: auto;
    box-shadow: none;
}

.articles-header-wrapper::after {
    display: none; /* Remove the overlay */
}

.articles-header .nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 2;
    width: 100%;
    padding-top: 2.5rem; /* Match the top padding from index.html */
}

/* Article Highlight */
.article-highlight {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 26.56px; /* Exact gap from Figma */
    padding: 91px 0 30px; /* Exact padding from Figma */
    width: 100%;
}

.highlight-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    gap: 10.1px; /* Exact gap from Figma */
}

.highlight-excerpt {
    color: #fff; /* White text for better visibility */
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-size: 27.45px; /* Exact size from Figma */
    font-weight: 400;
    line-height: 1.22;
    margin: 0;
}

.highlight-title {
    margin-top: auto;
    margin-bottom: 0;
    padding-bottom: 0;
    padding-top: 336.19px; /* Exact padding from Figma */
    margin-bottom: 56.66px; /* Exact margin from Figma */
    display: flex;
    flex-direction: column;
    align-self: stretch;
}

.highlight-link {
    color: #fff; /* White text for better visibility */
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-size: 51.85px; /* Exact size from Figma */
    font-weight: 400;
    line-height: 1.23;
    text-decoration: none;
}

.highlight-meta {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10.1px; /* Exact gap from Figma */
    justify-content: space-between;
    width: 100%;
}

.meta-primary {
    display: flex;
    align-items: center;
}

.meta-link {
    color: #fff; /* White text for better visibility */
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-size: 15.88px; /* Exact size from Figma */
    font-weight: 400;
    line-height: 1.51;
    text-decoration: none;
}

.meta-info {
    display: flex;
    align-items: center;
    gap: 10.07px; /* Exact gap from Figma */
    margin-left: auto; /* Push to the right */
}

.meta-item-group {
    display: flex;
    align-items: center;
    gap: 10.07px; /* Exact gap from Figma */
}

.meta-item {
    color: #fff; /* White text for better visibility */
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    line-height: 1.5;
}

.author-link {
    font-size: 16px; /* Exact size from Figma */
}

.category-link {
    font-size: 15.88px; /* Exact size from Figma */
}

.post-date {
    font-size: 15.38px; /* Exact size from Figma */
    opacity: 0.7;
}

.highlight-image {
    position: relative;
    width: 100%;
    height: 100%;
}

.feature-image-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 4px; /* Exact radius from Figma */
}

.placeholder-image {
    width: 100%;
    height: 100%;
    min-height: 400px;
    background-color: rgba(255, 255, 255, 0.2); /* Semi-transparent white */
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.placeholder-image::after {
    content: 'Featured Image';
    color: rgba(255, 255, 255, 0.5);
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-size: 1.25rem;
    font-weight: 400;
}

.feature-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px; /* Exact radius from Figma */
}

/* Category-specific header colors */
.articles-header.artikel {
    background-color: #12154B;
}

.articles-header.internes {
    background-color: #22A094;
}

.articles-header.weltgeschehen {
    background-color: #5A67D8;
}

.articles-header.friedenszeitung {
    background-color: #D84406;
}

.articles-header.events {
    background-color: #38A169;
}

.articles-header.technik {
    background-color: #805AD5;
}

.articles-header.politik {
    background-color: #E53E3E;
}

.articles-header.wirtschaft {
    background-color: #DD6B20;
}

/* Article Content Section */
.article-content {
    padding: 0 0.5rem 2.5rem;
}

.content-wrapper {
    padding: 0 var(--inner-padding, 2.8125rem);
}

/* Article Filters */
.article-filters {
    margin-bottom: 2.5rem;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1.25rem;
    width: 100%;
}

.article-header {
    display: flex;
    flex-direction: row;
    align-items: baseline;
}

.article-heading {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-size: 28px; /* Exact size from Figma */
    font-weight: 400;
    line-height: 1.198;
    color: #000;
    margin: 0;
    padding: 0;
}

.article-count {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-size: 12.99px; /* Exact size from Figma */
    font-weight: 400;
    line-height: 0.962;
    color: #000;
    margin-left: 0.25rem;
}

.filter-container {
    flex-grow: 1;
    display: flex;
    justify-content: flex-end; /* Align the container contents to the right */
    width: auto; /* Allow the container to shrink */
}

.filter-list {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    gap: 0.625rem;
    list-style: none;
    padding: 0;
    margin: 0;
    overflow-x: auto;
    padding-bottom: 0.5rem;
    justify-content: flex-end; /* Align the list items to the right */
    max-width: 100%; /* Ensure the list doesn't overflow its container */
}

.filter-item {
    margin: 0;
}

.filter-link {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15.04px 18.32px; /* Exact padding from Figma */
    background-color: #EAEFDE;
    color: #000;
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-size: 16px; /* Exact size from Figma */
    font-weight: 400;
    line-height: 1.5;
    text-decoration: none;
    border-radius: 10.08px; /* Exact radius from Figma */
    transition: background-color 0.3s ease, color 0.3s ease;
    white-space: nowrap;
}

.filter-item.active .filter-link {
    background-color: #000;
    color: #fff;
}

.filter-link:hover {
    background-color: #D8DFCA;
}

.filter-item.active .filter-link:hover {
    background-color: #333;
}

/* Article Grid - Fluid Scaling */
.article-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--article-gap-row) var(--article-gap-col); /* Use CSS variables */
    margin-bottom: 3rem;
    width: 100%;
    max-width: 100%; /* Allow full width expansion */
}

.article-card {
    display: flex;
    flex-direction: column;
    gap: var(--article-card-gap); /* Use CSS variable */
    width: 100%;
    max-width: none; /* Remove fixed max-width to allow fluid scaling */
}

.article-link-container {
    width: 100%;
    aspect-ratio: 16 / 18; /* Preserve aspect ratio instead of fixed height */
    height: auto;
}

.article-card-link {
    display: block;
    width: 100%;
    height: 100%;
    text-decoration: none;
    overflow: hidden;
}

.article-card-image {
    width: 100%;
    height: 100%;
    aspect-ratio: 16 / 18; /* Match container aspect ratio */
    overflow: hidden;
    border-radius: 4px;
}

.article-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.article-card-link:hover .article-img {
    transform: scale(1.05);
}

.article-card-content {
    display: flex;
    flex-direction: column;
    gap: var(--article-card-gap); /* Use CSS variable */
    padding-right: var(--article-content-padding); /* Use CSS variable */
}

.article-card-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--article-meta-gap); /* Use CSS variable */
    width: 100%;
}

.article-category {
    display: inline-flex;
    padding: 0 10.25px; /* Exact padding from Figma */
    justify-content: center;
    align-items: center;
    background-color: #12154B;
    border-radius: 1475.93px; /* Very large radius to ensure pill shape, as specified in Figma */
    color: #fff;
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-size: var(--article-meta-size); /* Use CSS variable */
    font-weight: 400;
    line-height: 1.84;
    letter-spacing: 3.16%;
    text-transform: uppercase;
    text-decoration: none;
    height: 19px; /* Exact height from Figma */
}

.article-date {
    color: #000;
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-size: var(--article-meta-size); /* Use CSS variable */
    font-weight: 400;
    line-height: 1.52;
    opacity: 0.5;
}

.article-card-body {
    display: flex;
    flex-direction: column;
    gap: 18.3px; /* Exact gap from Figma */
}

.article-card-title {
    margin: 0;
    padding: 0;
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-size: var(--article-title-size); /* Use CSS variable */
    font-weight: 400;
    line-height: 1.31;
    letter-spacing: 1.01%;
}

.article-card-title a {
    color: #000;
    text-decoration: none;
}

.article-card-title a:hover {
    text-decoration: underline;
}

.article-card-excerpt {
    margin: 0;
    padding: 0;
    color: #000;
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-size: var(--article-excerpt-size); /* Use CSS variable */
    font-weight: 400;
    line-height: 1.5;
    opacity: 0.5;
}

/* Custom Category Colors */
.article-category.artikel {
    background-color: #12154B;
}

.article-category.internes {
    background-color: #22A094;
}

.article-category.weltgeschehen {
    background-color: #5A67D8;
}

.article-category.friedenszeitung {
    background-color: #D84406;
}

.article-category.events {
    background-color: #38A169;
}

.article-category.technik {
    background-color: #805AD5;
}

.article-category.politik {
    background-color: #E53E3E;
}

.article-category.wirtschaft {
    background-color: #DD6B20;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1.25rem;
    margin-top: 3rem;
    margin-bottom: 2rem;
}

.pagination-prev,
.pagination-next {
    padding: 0.75rem 1.25rem;
    background-color: #EAEFDE;
    border: none;
    border-radius: 0.5rem;
    color: #000;
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-size: 1rem;
    font-weight: 400;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.pagination-prev:hover,
.pagination-next:hover {
    background-color: #D8DFCA;
}

.pagination-prev:disabled,
.pagination-next:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-numbers {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-link {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    color: #000;
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-size: 1rem;
    font-weight: 400;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.pagination-link:hover {
    background-color: #EAEFDE;
}

.pagination-link.active {
    background-color: #000;
    color: #fff;
}

.pagination-ellipsis {
    color: #000;
    font-size: 1.25rem;
    line-height: 1;
}

/* Responsive breakpoints - updated for fluid scaling */
@media (min-width: 1440px) {
    :root {
        /* Enhanced spacing for larger screens */
        --article-gap-row: clamp(2.5rem, 4vw, 6rem);
        --article-gap-col: clamp(1.8rem, 2.5vw, 3.5rem);
    }
    
    .article-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 1439px) and (min-width: 992px) {
    :root {
        /* Adjusted spacing for medium-large screens */
        --article-gap-row: clamp(2rem, 3vw, 4rem);
        --article-gap-col: clamp(1.5rem, 2vw, 2.5rem);
    }
    
    .article-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 991px) and (min-width: 768px) {
    :root {
        /* Adjusted spacing for medium screens */
        --article-gap-row: clamp(1.8rem, 2.5vw, 3rem);
        --article-gap-col: clamp(1.2rem, 1.5vw, 2rem);
        --article-padding: clamp(1.2rem, 3vw, 2rem);
    }
    
    .article-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 767px) {
    :root {
        /* Adjusted spacing for small screens */
        --article-gap-row: clamp(1.5rem, 4vw, 2.5rem);
        --article-gap-col: clamp(1rem, 2vw, 1.5rem);
        --article-padding: 1rem;
        --article-content-padding: 0;
    }
    
    .article-grid {
        grid-template-columns: 1fr;
        max-width: 500px; /* Limit width for better readability on mobile */
        margin-left: auto;
        margin-right: auto;
        padding: 0 1rem; /* Fixed padding on mobile */
    }
    
    .article-card {
        max-width: 100%;
    }
    
    .article-card-image, .article-link-container {
        /* Slightly taller aspect ratio for mobile */
        aspect-ratio: 16 / 14;
        height: auto;
    }
    
    .article-card-content {
        padding-right: 0;
    }
    
    .article-card-title {
        font-size: clamp(1.1rem, 4vw, 1.25rem);
    }
    
    .article-card-meta {
        flex-wrap: wrap;
        gap: 8px;
    }
}

@media (max-width: 576px) {
    :root {
        /* Minimal spacing for very small screens */
        --article-gap-row: 1.5rem;
        --article-padding: 0.8rem;
        --article-card-gap: 0.8rem;
        --article-meta-gap: 0.5rem;
    }
    
    .articles-header {
        margin: 0;
        border-radius: 0;
    }
    
    .articles-header-wrapper {
        padding: 0 15px;
    }
    
    .article-highlight {
        padding: 30px 0 20px;
    }
    
    .highlight-meta {
        margin-top: 10px;
        flex-direction: column;
        align-items: flex-start;
    }
    
    .meta-info {
        margin-top: 10px;
        flex-wrap: wrap;
        gap: 5px;
        width: 100%;
        justify-content: flex-start;
    }
    
    .meta-item-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .placeholder-image {
        min-height: 200px;
    }
    
    .highlight-link {
        font-size: 24px;
    }
    
    .highlight-excerpt {
        font-size: 18px;
    }
    
    .highlight-title {
        padding-top: 20px;
    }
    
    .article-heading {
        font-size: 20px;
    }
    
    .article-count {
        font-size: 14px;
    }
    
    .filter-link {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    .article-card-image, .article-link-container {
        /* Even shorter aspect ratio for small mobile */
        aspect-ratio: 16 / 12;
        height: auto;
    }
    
    .article-card-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
    }
}

.articles-header .logo-svg {
    filter: brightness(0) invert(1); /* Makes SVG white */
}

.articles-header .menu a {
    color: #fff;
}

.articles-header .dropdown-indicator-svg {
    filter: brightness(0) invert(1); /* Makes SVG white */
}

.article-placeholder {
    width: 100%;
    height: 100%;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.article-placeholder::after {
    content: 'No Image';
    color: #888;
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-size: 1rem;
    font-weight: 400;
}

/* Responsive styles for article components */
@media (max-width: 1200px) {
    .article-card-image, .article-link-container {
        /* Use aspect ratio instead of fixed height */
        aspect-ratio: 16 / 16;
        height: auto;
    }
    
    .highlight-link {
        font-size: clamp(2rem, 3vw, 2.5rem);
    }
    
    .highlight-excerpt {
        font-size: clamp(1.25rem, 1.5vw, 1.5rem);
    }
    
    .highlight-title {
        padding-top: clamp(8rem, 15vw, 12.5rem);
    }
}

@media (max-width: 992px) {
    .article-highlight {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 60px 0 30px;
    }
    
    .highlight-image {
        order: -1;
    }
    
    .placeholder-image {
        min-height: 300px;
    }
    
    .highlight-title {
        margin-bottom: 30px;
        padding-top: 30px; /* Reset padding for smaller screens */
    }
    
    .highlight-link {
        font-size: 36px;
    }
    
    .highlight-excerpt {
        font-size: 22px;
    }
    
    .meta-info {
        gap: 10px;
    }
    
    .article-filters {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .article-header {
        width: 100%;
    }
    
    .filter-container {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .articles-header {
        margin: 0.25rem;
        margin-bottom: 2rem;
        border-radius: 6px;
    }
    
    .articles-header-wrapper {
        padding: 0 30px;
    }
    
    .article-highlight {
        padding: 40px 0 30px;
    }
    
    .placeholder-image {
        min-height: 250px;
    }
    
    .content-wrapper {
        padding: 0 1.5rem;
    }
    
    .highlight-link {
        font-size: 30px;
    }
    
    .highlight-excerpt {
        font-size: 20px;
    }
    
    .meta-link {
        font-size: 18px;
    }
    
    .article-card-image, .article-link-container {
        /* Slightly taller aspect ratio for mobile */
        aspect-ratio: 16 / 14;
        height: auto;
    }
    
    .article-heading {
        font-size: 24px;
    }
    
    .filter-link {
        padding: 12px 16px;
        font-size: 14px;
    }
    
    .pagination {
        flex-direction: column;
        gap: 1rem;
    }
    
    .pagination-numbers {
        order: -1;
    }
}

@media (max-width: 576px) {
    .article-card-image, .article-link-container {
        /* Even shorter aspect ratio for small mobile */
        aspect-ratio: 16 / 12;
        height: auto;
    }
} 
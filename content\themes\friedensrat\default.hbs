<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="description" content="{{@site.description}}">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="color-scheme" content="light">
    <title>{{meta_title}}</title>

    <!-- Font preloading for better rendering and reduced FOUT (Flash of Unstyled Text) -->
    <link rel="preload" href="{{asset "fonts/AtkinsonHyperlegibleNext-Regular.ttf"}}" as="font" type="font/ttf" crossorigin>
    <link rel="preload" href="{{asset "fonts/AtkinsonHyperlegibleNext-Medium.ttf"}}" as="font" type="font/ttf" crossorigin>
    <link rel="preload" href="{{asset "fonts/AtkinsonHyperlegibleNext-SemiBold.ttf"}}" as="font" type="font/ttf" crossorigin>
    <link rel="preload" href="{{asset "fonts/AtkinsonHyperlegibleNext-Bold.ttf"}}" as="font" type="font/ttf" crossorigin>
    <link rel="preload" href="https://fonts.gstatic.com/s/albertsans/v1/i7dOIFdwYjGaAMFtZd_QA1ZVYFeQGQyUV6U.woff2" as="font" type="font/woff2" crossorigin>

    <link rel="stylesheet" href="{{asset "css/normalize.css"}}">
    <link rel="stylesheet" href="{{asset "css/fonts.css"}}">
    <link rel="stylesheet" href="{{asset "css/style.css"}}">
    <link rel="stylesheet" href="{{asset "css/fluid-typography.css"}}">
    <link rel="stylesheet" href="{{asset "css/main.css"}}">
    <link rel="stylesheet" href="{{asset "css/responsive.css"}}">
    <link rel="stylesheet" href="{{asset "css/mobile-nav.css"}}">
    <link rel="stylesheet" href="{{asset "css/koenig.css"}}">
    {{#is "index,tag,author"}}
        <link rel="stylesheet" href="{{asset "css/articles.css"}}">
    {{/is}}
    {{#is "page" slug="articles"}}
        <link rel="stylesheet" href="{{asset "css/articles.css"}}">
    {{/is}}

    <style>
        /* Ensure smooth font rendering on all browsers */
        html {
            -webkit-text-size-adjust: 100%;
            text-size-adjust: 100%;
        }
    </style>

    {{ghost_head}}
</head>
<body class="{{body_class}}">
    {{> "mobile-navigation"}}

    <div class="main">
        {{#unless (is "page" slug="articles")}}
            {{#unless (is "tag")}}
                {{#unless (is "author")}}
                    {{> "site-header"}}
                {{/unless}}
            {{/unless}}
        {{/unless}}

        {{{body}}}

        {{> "site-footer"}}
    </div>

    {{ghost_foot}}
    <script src="{{asset "js/main.js"}}"></script>
    <script src="{{asset "js/mobile-nav.js"}}"></script>
    {{#is "index,tag,author"}}
        <script src="{{asset "js/articles.js"}}"></script>
    {{/is}}
    {{#is "page" slug="articles"}}
        <script src="{{asset "js/articles.js"}}"></script>
    {{/is}}
</body>
</html>

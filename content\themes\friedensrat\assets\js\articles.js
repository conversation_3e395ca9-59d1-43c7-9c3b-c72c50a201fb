/**
 * Articles Page Functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize any articles page specific functionality
    initFilterTags();
    initPagination();
});

/**
 * Initialize the filter tags functionality
 */
function initFilterTags() {
    // Hide event tag filters on page load
    hideEventFilters();

    const filterItems = document.querySelectorAll('.filter-item');

    filterItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Only prevent default if we're on the articles page (not tag pages)
            if (window.location.pathname === '/articles/') {
                e.preventDefault();

                // Remove active class from all items
                filterItems.forEach(i => i.classList.remove('active'));

                // Add active class to clicked item
                this.classList.add('active');

                // Get the filter link
                const filterLink = this.querySelector('.filter-link');
                const filterHref = filterLink.getAttribute('href');

                // Check if it's the "Alle Artikel" filter
                if (filterHref.includes('/articles/')) {
                    // Show all articles
                    filterArticles('all');
                } else {
                    // Extract tag slug from href (e.g., /tag/artikel/ -> artikel)
                    const tagSlug = filterHref.split('/tag/')[1]?.replace('/', '');
                    if (tagSlug) {
                        filterArticles(tagSlug);
                    }
                }
            }
            // If not on articles page, let the link navigate normally
        });
    });
}

/**
 * Hide event tag filters since events shouldn't appear in article filters
 */
function hideEventFilters() {
    const filterItems = document.querySelectorAll('.filter-item');
    filterItems.forEach(item => {
        const filterLink = item.querySelector('.filter-link');
        if (filterLink && filterLink.getAttribute('href').includes('/tag/event')) {
            item.style.display = 'none';
        }
    });
}

/**
 * Filter articles based on tag slug
 */
function filterArticles(tagSlug) {
    const articleCards = document.querySelectorAll('.article-card');
    let visibleCount = 0;

    articleCards.forEach(card => {
        if (tagSlug === 'all') {
            // Show all articles
            card.style.display = 'block';
            visibleCount++;
        } else {
            // Check if the article has the matching tag
            const categoryEl = card.querySelector('.article-category');
            if (categoryEl) {
                // Get the href from the category link to extract the tag slug
                const categoryHref = categoryEl.getAttribute('href');
                const articleTagSlug = categoryHref ? categoryHref.split('/tag/')[1]?.replace('/', '') : '';

                if (articleTagSlug === tagSlug) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            } else {
                // If no category, hide the article when filtering
                card.style.display = 'none';
            }
        }
    });

    // Update the article count
    updateArticleCount(visibleCount, tagSlug);

    // Hide pagination when filtering (since we're showing filtered results)
    const pagination = document.querySelector('.pagination');
    if (pagination) {
        pagination.style.display = tagSlug === 'all' ? 'flex' : 'none';
    }
}

/**
 * Update the article count display
 */
function updateArticleCount(count, tagSlug) {
    const articleCountEl = document.querySelector('.article-count');
    if (articleCountEl) {
        articleCountEl.textContent = count;
    }

    // Update the heading text
    const articleHeading = document.querySelector('.article-heading');
    if (articleHeading && tagSlug !== 'all') {
        const tagName = document.querySelector(`.filter-item.active .filter-link`);
        if (tagName && tagName.textContent !== 'Alle Artikel') {
            articleHeading.innerHTML = `${tagName.textContent} <span class="article-count">${count}</span>`;
        }
    } else if (articleHeading && tagSlug === 'all') {
        articleHeading.innerHTML = `Alle Artikel <span class="article-count">${count}</span>`;
    }
}

/**
 * Initialize pagination functionality
 */
function initPagination() {
    const paginationLinks = document.querySelectorAll('.pagination-link');
    const prevButton = document.querySelector('.pagination-prev');
    const nextButton = document.querySelector('.pagination-next');

    // Add click event to pagination links
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all links
            paginationLinks.forEach(l => l.classList.remove('active'));

            // Add active class to clicked link
            this.classList.add('active');

            // Get the page number
            const pageNumber = parseInt(this.textContent);

            // Enable/disable prev/next buttons based on page number
            prevButton.disabled = pageNumber === 1;
            nextButton.disabled = pageNumber === paginationLinks.length;

            // In the static template, we're just logging the page number
            // In the Ghost CMS implementation, this would trigger an AJAX request
            // or change the URL parameter to load the appropriate page
            console.log('Page selected:', pageNumber);

            // Scroll to top of the articles section
            document.querySelector('.article-content').scrollIntoView({ behavior: 'smooth' });
        });
    });

    // Add click event to prev button
    if (prevButton) {
        prevButton.addEventListener('click', function(e) {
            if (this.disabled) return;

            const activeLink = document.querySelector('.pagination-link.active');
            if (activeLink) {
                const prevLink = activeLink.previousElementSibling;
                if (prevLink && prevLink.classList.contains('pagination-link')) {
                    prevLink.click();
                }
            }
        });
    }

    // Add click event to next button
    if (nextButton) {
        nextButton.addEventListener('click', function(e) {
            if (this.disabled) return;

            const activeLink = document.querySelector('.pagination-link.active');
            if (activeLink) {
                const nextLink = activeLink.nextElementSibling;
                if (nextLink && nextLink.classList.contains('pagination-link')) {
                    nextLink.click();
                }
            }
        });
    }
}

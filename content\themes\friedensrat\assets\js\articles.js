/**
 * Articles Page Functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize any articles page specific functionality
    initFilterTags();
    initPagination();
});

/**
 * Initialize the filter tags functionality
 */
function initFilterTags() {
    const filterItems = document.querySelectorAll('.filter-item');
    
    filterItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all items
            filterItems.forEach(i => i.classList.remove('active'));
            
            // Add active class to clicked item
            this.classList.add('active');
            
            // Get the filter value
            const filterValue = this.querySelector('.filter-link').textContent.trim().toLowerCase();
            
            // In the static template, we're just logging the filter value
            // In the Ghost CMS implementation, this would trigger an AJAX request
            // or change the URL parameter to filter the posts
            console.log('Filter selected:', filterValue);
            
            // For demo purposes, highlight matching categories
            highlightMatchingCategories(filterValue);
        });
    });
}

/**
 * Highlight articles that match the selected filter
 * This is for demo purposes only in the static template
 */
function highlightMatchingCategories(filterValue) {
    const articleCards = document.querySelectorAll('.article-card');
    
    if (filterValue === 'all') {
        // Show all articles
        articleCards.forEach(card => {
            card.style.opacity = '1';
        });
        return;
    }
    
    // For each article, check if it matches the filter
    articleCards.forEach(card => {
        const categoryEl = card.querySelector('.card-category');
        if (categoryEl) {
            const category = categoryEl.textContent.trim().toLowerCase();
            
            if (category === filterValue) {
                card.style.opacity = '1';
            } else {
                card.style.opacity = '0.5';
            }
        }
    });
}

/**
 * Initialize pagination functionality
 */
function initPagination() {
    const paginationLinks = document.querySelectorAll('.pagination-link');
    const prevButton = document.querySelector('.pagination-prev');
    const nextButton = document.querySelector('.pagination-next');
    
    // Add click event to pagination links
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all links
            paginationLinks.forEach(l => l.classList.remove('active'));
            
            // Add active class to clicked link
            this.classList.add('active');
            
            // Get the page number
            const pageNumber = parseInt(this.textContent);
            
            // Enable/disable prev/next buttons based on page number
            prevButton.disabled = pageNumber === 1;
            nextButton.disabled = pageNumber === paginationLinks.length;
            
            // In the static template, we're just logging the page number
            // In the Ghost CMS implementation, this would trigger an AJAX request
            // or change the URL parameter to load the appropriate page
            console.log('Page selected:', pageNumber);
            
            // Scroll to top of the articles section
            document.querySelector('.article-content').scrollIntoView({ behavior: 'smooth' });
        });
    });
    
    // Add click event to prev button
    if (prevButton) {
        prevButton.addEventListener('click', function(e) {
            if (this.disabled) return;
            
            const activeLink = document.querySelector('.pagination-link.active');
            if (activeLink) {
                const prevLink = activeLink.previousElementSibling;
                if (prevLink && prevLink.classList.contains('pagination-link')) {
                    prevLink.click();
                }
            }
        });
    }
    
    // Add click event to next button
    if (nextButton) {
        nextButton.addEventListener('click', function(e) {
            if (this.disabled) return;
            
            const activeLink = document.querySelector('.pagination-link.active');
            if (activeLink) {
                const nextLink = activeLink.nextElementSibling;
                if (nextLink && nextLink.classList.contains('pagination-link')) {
                    nextLink.click();
                }
            }
        });
    }
}

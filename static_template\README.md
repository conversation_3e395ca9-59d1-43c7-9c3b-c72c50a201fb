# Schweizerischer Friedensrat - Static HTML Template

This is a static HTML template for the Schweizerischer Friedensrat website based on a Figma design. The template is designed to be later converted to a Ghost CMS theme.

## Project Structure

```
static_template/
├── css/
│   ├── fonts.css           # Font definitions
│   ├── fluid-typography.css # Fluid typography system
│   ├── main.css            # Base site-wide styles
│   ├── mobile-nav.css      # Mobile navigation styles
│   ├── normalize.css       # CSS reset
│   ├── responsive.css      # Media queries and responsive styles
│   └── style.css           # Component-specific styles
├── fonts/                  # Atkinson Hyperlegible Next font files
├── img/                    # Image assets
│   ├── article-images/     # Article thumbnail images
│   └── team/               # Team member portraits
├── js/
│   ├── main.js             # Main JavaScript functionality
│   └── mobile-nav.js       # Mobile navigation JavaScript
└── index.html              # Main HTML file
```

## Features

1. Responsive design (completed)
   - Mobile and desktop layouts using CSS Grid and Flexbox
   - Fluid typography system adapting to viewport width
   - Mobile-first approach ensuring usability on all devices
   - Custom breakpoints at logical screen sizes, not arbitrary pixel values
   - Mobile menu that transforms into desktop navigation at larger screens
   - Mobile dropdown navigation with interactive toggle support (completed)
   - Added Ghost CMS templating comments for navigation (completed)

2. Fluid article grid (completed)
   - Responsive grid layout showing 4 columns on large screens (≥1440px), 3 on medium screens, 2 on tablets, and 1 on mobile
   - Uses CSS variables and clamp() functions for fluid scaling of grid spacing, typography, and layout elements
   - Grid expands with the viewport on large screens rather than being constrained to a fixed width
   - Uses aspect ratios instead of fixed heights for article images, ensuring proper scaling
   - Consistent visual rhythm maintained across all screen sizes
   - Prepared for Ghost CMS integration with template comments

3. Uses Atkinson Hyperlegible Next as the primary font
- Semantic HTML5 markup
- Modern CSS with flexbox

## Development

This template is being developed incrementally, section by section:

1. Header section (completed)
   - Responsive design with mobile adaptations
   - Proper navigation and featured content area
   - Using Atkinson Hyperlegible Next font family

2. Article slider section (completed)
   - Horizontally scrollable content
   - Navigation controls with state indicators
   - Varying article image heights for visual interest
   - Touch support for mobile devices

3. Friedensrat highlights section (completed)
   - Two cards with images and text
   - Different layout on mobile vs desktop
   - CSS Grid layout for flexible positioning

4. Events section (completed)
   - Three responsive event cards
   - Hover effects for improved interactivity
   - Structured for Ghost CMS integration

5. Offers section (completed)
   - Large fluid headline that spans full width
   - Three colorful offer cards with hover effects
   - Color transitions on interaction

6. Team section (completed)
   - Implemented according to exact Figma design specifications
   - Interactive team member portraits with smooth transitions
   - Dynamic quote display with proper typography
   - Updated portrait image `src` attributes to use actual images from `img/team/` folder.
   - Vertical structure with team selection below quote content
   - Proper spacing and margins consistent with other sections
   - Fully responsive layout for all screen sizes
   - Prepared for Ghost CMS integration with template comments

7. Newsletter section (completed)
   - Implemented according to Figma design specifications with dark blue background
   - Email input form with submit button and privacy notice
   - Responsive design that adapts to mobile devices
   - Uses consistent styling with other sections
   - Prepared for Ghost CMS integration with member subscription system
   - Fluid typography for better readability across screen sizes

8. Footer section (completed)

9. All Articles page (completed)
   - Implemented a dedicated page for browsing all articles
   - Featured article highlight section at the top with custom category-based colors
   - Filtering system with category tags for content organization
   - Fluid article grid layout that adapts to screen size (4-column on large screens to 1-column on mobile)
   - Uses CSS variables and clamp() functions for fluid scaling of elements
   - Proper pagination controls for multi-page navigation
   - Aspect ratio-based responsive images instead of fixed heights
   - Comprehensive Ghost CMS conversion notes for each component

## Technical Implementation Details

- **Responsive Design**: All sections adapt to various screen sizes using media queries and fluid sizing
- **Interactive Elements**: JavaScript is used for slider navigation, team member selection, and other interactive features
- **CSS Architecture**: Modern CSS with Grid, Flexbox, and custom properties for theming
- **Accessibility**: Semantic HTML structure with proper ARIA attributes where needed
- **Performance**: Optimized images and minimal dependencies for fast loading
- **CMS Integration**: Each section includes conversion notes for Ghost CMS implementation
- **Fluid Typography**: Implemented a Finsweet-inspired fluid typography system that scales all text elements based on viewport width. This system uses:
  - Base typography variables in a dedicated CSS file (`css/fluid-typography.css`)
  - Precise mathematical calculations to smoothly scale the root font size across different screen sizes
  - Viewport-based scaling ensures type is perfectly sized at all screen widths
  - Maintains consistent proportions through breakpoints while respecting min/max sizes

## Usage

To preview the template, simply open the `index.html` file in a web browser.

## Credits

- Design: Friedensrat Webseite Figma design
- Fonts: Atkinson Hyperlegible Next
- Development: [Your Name/Organization]
{{!< default}}

<!-- Article Page Header Section -->
<header class="articles-header {{tag.slug}}">
    <div class="articles-header-wrapper">
        <nav class="nav">
            <div class="logo-container">
                <a href="{{@site.url}}" class="logo-link">
                    <img src="{{asset "images/logo.svg"}}" alt="{{@site.title}}" class="logo-svg">
                </a>
            </div>
            <!-- Desktop Menu -->
            <ul class="menu">
                {{navigation}}
            </ul>
        </nav>

        <!-- Article Highlight Container -->
        {{#if posts}}
            {{#with posts.[0]}}
                <div class="article-highlight">
                    <div class="highlight-content">
                        <p class="highlight-excerpt">
                            {{#if excerpt}}{{excerpt words="25"}}{{else}}{{content words="25"}}{{/if}}
                        </p>
                        <div class="highlight-title">
                            <a href="{{url}}" class="highlight-link">
                                {{title}}
                            </a>
                        </div>
                        <div class="highlight-meta">
                            <div class="meta-primary">
                                <a href="{{url}}" class="meta-link">Mehr lesen</a>
                            </div>
                            <div class="meta-info">
                                <div class="meta-item-group">
                                    {{#if primary_author}}
                                        <span class="meta-item author-link">{{primary_author.name}}</span>
                                    {{/if}}
                                    <span class="meta-item category-link">{{../tag.name}}</span>
                                </div>
                                <span class="meta-item post-date">{{date format="MMM YYYY"}}</span>
                            </div>
                        </div>
                    </div>
                    <div class="highlight-image">
                        <div class="feature-image-container">
                            {{#if feature_image}}
                                <img src="{{img_url feature_image size="l"}}" alt="{{title}}" class="feature-image">
                            {{else}}
                                <div class="placeholder-image"></div>
                            {{/if}}
                        </div>
                    </div>
                </div>
            {{/with}}
        {{/if}}
    </div>
</header>

<!-- Article Filters and Grid Section -->
<main class="article-content">
    <div class="content-wrapper">
        <!-- Article Filters -->
        <div class="article-filters">
            <div class="article-header">
                <h2 class="article-heading">{{tag.name}} <span class="article-count">{{pagination.total}}</span></h2>
            </div>
            <div class="filter-container">
                <ul class="filter-list">
                    <li class="filter-item">
                        <a href="{{@site.url}}/articles/" class="filter-link">All</a>
                    </li>
                    {{#get "tags" limit="all" include="count.posts" filter="visibility:public" order="count.posts desc"}}
                        {{#foreach tags}}
                            {{#unless slug "event"}}
                                <li class="filter-item">
                                    <a href="{{url}}" class="filter-link">{{name}}</a>
                                </li>
                            {{/unless}}
                        {{/foreach}}
                    {{/get}}
                </ul>
            </div>
        </div>

        <!-- Article Grid -->
        <div class="article-grid">
            {{#foreach posts}}
                <article class="article-card">
                    <div class="article-link-container">
                        <a href="{{url}}" class="article-card-link">
                            <div class="article-card-image">
                                {{#if feature_image}}
                                    <img src="{{img_url feature_image size="m"}}" alt="{{title}}" class="article-img">
                                {{else}}
                                    <div class="article-placeholder"></div>
                                {{/if}}
                            </div>
                        </a>
                    </div>
                    <div class="article-card-content">
                        <div class="article-card-meta">
                            {{#if primary_tag}}
                                <a href="{{primary_tag.url}}" class="article-category {{primary_tag.slug}}">{{primary_tag.name}}</a>
                            {{else}}
                                <a href="#" class="article-category artikel">Artikel</a>
                            {{/if}}
                            <span class="article-date">{{date format="MMM YYYY"}}</span>
                        </div>
                        <div class="article-card-body">
                            <h3 class="article-card-title">
                                <a href="{{url}}">{{title}}</a>
                            </h3>
                            {{#if excerpt}}
                                <p class="article-card-excerpt">{{excerpt characters="150"}}</p>
                            {{/if}}
                        </div>
                    </div>
                </article>
            {{/foreach}}
        </div>

        <!-- Pagination -->
        {{#if pagination.pages}}
            <div class="pagination">
                {{#if pagination.prev}}
                    <a href="{{page_url pagination.prev}}" class="pagination-prev">Zurück</a>
                {{else}}
                    <button class="pagination-prev" disabled>Zurück</button>
                {{/if}}

                <div class="pagination-numbers">
                    {{#foreach pagination.pages}}
                        {{#if current}}
                            <span class="pagination-link active">{{number}}</span>
                        {{else}}
                            <a href="{{url}}" class="pagination-link">{{number}}</a>
                        {{/if}}
                    {{/foreach}}
                </div>

                {{#if pagination.next}}
                    <a href="{{page_url pagination.next}}" class="pagination-next">Weiter</a>
                {{else}}
                    <button class="pagination-next" disabled>Weiter</button>
                {{/if}}
            </div>
        {{/if}}
    </div>
</main>

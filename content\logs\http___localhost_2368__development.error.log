{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":17372,"level":50,"version":"5.115.1","req":{"meta":{"requestId":"fcc13942-0f2e-4755-bb5b-c20ccc006aea","userId":null},"url":"/users/me/?include=roles","method":"GET","originalUrl":"/ghost/api/admin/users/me/?include=roles","params":{},"headers":{"host":"localhost:2368","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0","accept":"application/json, text/javascript, */*; q=0.01","accept-language":"de,en-US;q=0.7,en;q=0.3","accept-encoding":"gzip, deflate, br, zstd","content-type":"application/json; charset=UTF-8","x-ghost-version":"5.115","app-pragma":"no-cache","x-requested-with":"XMLHttpRequest","connection":"keep-alive","referer":"http://localhost:2368/ghost/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin"},"query":{"include":"roles"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.115","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"343","etag":"W/\"157-2/9EttBS7EwbwMci5MkqFkyYxNQ\""},"statusCode":403,"responseTime":"7650ms"},"err":{"id":"1d9e63e0-0ebe-11f0-8ec8-a366300032d7","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated user or integration. Check that cookies are being passed through if using session authentication.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeAdminApi (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\auth\\authorize.js:33:25)\n    at Layer.handle [as handle_request] (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticate (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\auth\\session\\middleware.js:54:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-04-01T05:56:55.206Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":17372,"level":50,"version":"5.115.1","req":{"meta":{"requestId":"fd9d3eea-d121-4117-9076-d1f72cfeafba","userId":null},"url":"/users/me/?include=roles","method":"GET","originalUrl":"/ghost/api/admin/users/me/?include=roles","params":{},"headers":{"host":"localhost:2368","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0","accept":"application/json, text/javascript, */*; q=0.01","accept-language":"de,en-US;q=0.7,en;q=0.3","accept-encoding":"gzip, deflate, br, zstd","content-type":"application/json; charset=UTF-8","x-ghost-version":"5.115","app-pragma":"no-cache","x-requested-with":"XMLHttpRequest","connection":"keep-alive","referer":"http://localhost:2368/ghost/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin"},"query":{"include":"roles"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.115","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"343","etag":"W/\"157-sHimVP99e7kaQnh/jNfUPwYaTUA\""},"statusCode":403,"responseTime":"4ms"},"err":{"id":"1da6c850-0ebe-11f0-8ec8-a366300032d7","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated user or integration. Check that cookies are being passed through if using session authentication.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeAdminApi (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\auth\\authorize.js:33:25)\n    at Layer.handle [as handle_request] (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticate (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\auth\\session\\middleware.js:54:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-04-01T05:56:55.256Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":17372,"level":50,"version":"5.115.1","err":{"id":"26954fe0-0ebe-11f0-8ec8-a366300032d7","domain":"http://localhost:2368/","code":null,"name":"EmailError","statusCode":500,"level":"normal","message":"Failed to send email. Reason: Email has been temporarily rejected.","context":"\"Unable to send welcome email, your site will continue to function.\"","help":"\"Please see https://ghost.org/docs/config/#mail for instructions on configuring email.\"","stack":"EmailError: Failed to send email. Reason: Email has been temporarily rejected.\n    at createMailError (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\mail\\GhostMailer.js:81:12)\n    at GhostMailer.handleDirectTransportResponse (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\mail\\GhostMailer.js:171:19)\n    at GhostMailer.send (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\mail\\GhostMailer.js:133:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:34)\n    at async Object.ImplWrapper [as send] (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:264:30)\n    at async C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\auth\\setup.js:186:17","hideStack":false},"msg":"Failed to send email. Reason: Email has been temporarily rejected.","time":"2025-04-01T05:57:10.239Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":25224,"level":50,"version":"5.115.1","req":{"meta":{"requestId":"9c8630a2-3bb9-423a-a91c-6b2e2b4efb12","userId":null},"url":"/users/me/?include=roles","method":"GET","originalUrl":"/ghost/api/admin/users/me/?include=roles","params":{},"headers":{"host":"localhost:2368","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0","accept":"application/json, text/javascript, */*; q=0.01","accept-language":"de,en-US;q=0.7,en;q=0.3","accept-encoding":"gzip, deflate, br, zstd","content-type":"application/json; charset=UTF-8","x-ghost-version":"5.115","app-pragma":"no-cache","x-requested-with":"XMLHttpRequest","connection":"keep-alive","referer":"http://localhost:2368/ghost/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin"},"query":{"include":"roles"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.115","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"343","etag":"W/\"157-E50H9e65ocDQYR5XLo4NntqrAho\""},"statusCode":403,"responseTime":"7990ms"},"err":{"id":"be7cac90-28c7-11f0-93f3-1550d5b52fef","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated user or integration. Check that cookies are being passed through if using session authentication.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeAdminApi (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\auth\\authorize.js:33:25)\n    at Layer.handle [as handle_request] (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticate (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\auth\\session\\middleware.js:54:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-05-04T09:11:20.801Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":25224,"level":50,"version":"5.115.1","err":{"domain":"http://localhost:2368/","code":"ETIMEDOUT","message":"Timeout awaiting 'request' for 1000ms","context":"\"Checking for updates failed, your site will continue to function.\"","help":"\"If you get this error repeatedly, please seek help from https://ghost.org/docs/\"","stack":"RequestError: Timeout awaiting 'request' for 1000ms\n    at ClientRequest.<anonymous> (file:///C:/ghost_test/versions/5.115.1/node_modules/@tryghost/request/node_modules/got/dist/source/core/index.js:792:61)\n    at Object.onceWrapper (node:events:634:26)\n    at ClientRequest.emit (node:events:531:35)\n    at emitErrorEvent (node:_http_client:108:11)\n    at TLSSocket.socketErrorListener (node:_http_client:511:5)\n    at TLSSocket.emit (node:events:519:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at runNextTicks (node:internal/process/task_queues:64:3)\n    at process.processTimers (node:internal/timers:516:9)\n    at Timeout.timeoutHandler [as _onTimeout] (file:///C:/ghost_test/versions/5.115.1/node_modules/@tryghost/request/node_modules/got/dist/source/core/timed-out.js:42:25)\n    at listOnTimeout (node:internal/timers:583:11)\n    at process.processTimers (node:internal/timers:519:7)"},"msg":"Timeout awaiting 'request' for 1000ms","time":"2025-05-04T09:11:20.832Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":25224,"level":50,"version":"5.115.1","req":{"meta":{"requestId":"92735141-2fab-4a8f-9e04-563f2c67a53d","userId":null},"url":"/users/me/?include=roles","method":"GET","originalUrl":"/ghost/api/admin/users/me/?include=roles","params":{},"headers":{"host":"localhost:2368","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0","accept":"application/json, text/javascript, */*; q=0.01","accept-language":"de,en-US;q=0.7,en;q=0.3","accept-encoding":"gzip, deflate, br, zstd","content-type":"application/json; charset=UTF-8","x-ghost-version":"5.115","app-pragma":"no-cache","x-requested-with":"XMLHttpRequest","connection":"keep-alive","referer":"http://localhost:2368/ghost/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin"},"query":{"include":"roles"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.115","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"343","etag":"W/\"157-myPqmSM3I7Ochz5wtPHZXN5c8u4\""},"statusCode":403,"responseTime":"4ms"},"err":{"id":"be87f730-28c7-11f0-93f3-1550d5b52fef","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated user or integration. Check that cookies are being passed through if using session authentication.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeAdminApi (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\auth\\authorize.js:33:25)\n    at Layer.handle [as handle_request] (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticate (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\auth\\session\\middleware.js:54:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-05-04T09:11:20.870Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":25224,"level":50,"version":"5.115.1","req":{"meta":{"requestId":"b9ba7ba8-f620-4acd-a7e0-3cf1699b9005","userId":null},"url":"/session","method":"POST","originalUrl":"/ghost/api/admin/session","params":{},"headers":{"host":"localhost:2368","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0","accept":"text/plain, */*; q=0.01","accept-language":"de,en-US;q=0.7,en;q=0.3","accept-encoding":"gzip, deflate, br, zstd","content-type":"application/json;charset=utf-8","x-ghost-version":"5.115","app-pragma":"no-cache","x-requested-with":"XMLHttpRequest","content-length":"54","origin":"http://localhost:2368","connection":"keep-alive","referer":"http://localhost:2368/ghost/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=0"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.115","vary":"Accept-Version, Origin, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"http://localhost:2368","content-type":"application/json; charset=utf-8","content-length":"321","etag":"W/\"141-BLpszEAuMGMBPT06yAc/YuhVaeQ\""},"statusCode":422,"responseTime":"664ms"},"err":{"id":"d3133660-28c7-11f0-93f3-1550d5b52fef","domain":"http://localhost:2368/","code":"PASSWORD_INCORRECT","name":"ValidationError","statusCode":422,"level":"normal","message":"Your password is incorrect.","context":"\"Your password is incorrect.\"","help":"\"Visit and save your profile after logging in to check for problems.\"","stack":"ValidationError: Your password is incorrect.\n    at C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\user.js:1011:39\nFrom previous event:\n    at Function.check (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\user.js:958:14)\n    at Child.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\session.js:40:32)\nFrom previous event:\n    at add (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\session.js:33:56)\n    at Http (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\http.js:70:34)\n    at Layer.handle [as handle_request] (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at module.exports.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-brute\\index.js:142:36)\n    at process.processImmediate (node:internal/timers:483:21)","hideStack":false},"msg":"Your password is incorrect.","time":"2025-05-04T09:11:55.339Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":17028,"level":50,"version":"5.115.1","req":{"meta":{"requestId":"7f8e1182-b11d-497a-a7e4-81e6422bcdae","userId":"1"},"url":"/themes/friedensrat/activate/","method":"PUT","originalUrl":"/ghost/api/admin/themes/friedensrat/activate/","params":{},"headers":{"host":"localhost:2368","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0","accept":"*/*","accept-language":"de,en-US;q=0.7,en;q=0.3","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:2368/ghost/","app-pragma":"no-cache","x-ghost-version":"5.115","origin":"http://localhost:2368","connection":"keep-alive","cookie":"**REDACTED**","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=0","content-length":"0"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.115","vary":"Accept-Version, Origin, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"http://localhost:2368","content-type":"application/json; charset=utf-8","etag":"W/\"237c-2Z0LnqVMCIHI7YggNNGTFn0asDQ\"","content-encoding":"br"},"statusCode":422,"responseTime":"108ms"},"err":{"id":"46e03910-28ca-11f0-a7d5-279253de5b21","domain":"http://localhost:2368/","code":null,"name":"ThemeValidationError","statusCode":422,"level":"normal","message":"Theme \"friedensrat\" is not compatible or contains errors.","stack":"ThemeValidationError: Theme \"friedensrat\" is not compatible or contains errors.\n    at getThemeValidationError (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\themes\\validate.js:144:12)\n    at Object.checkSafe (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\themes\\validate.js:140:11)\n    at async module.exports.activate (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\themes\\activate.js:57:26)\n    at async Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\themes.js:62:33)\n    at async getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:34)\n    at async ImplWrapper (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:264:30)\n    at async Http (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\http.js:70:28)","hideStack":false,"errorDetails":"{\"checkedVersion\":\"5.x\",\"name\":\"friedensrat\",\"path\":\"C:\\\\ghost_test\\\\content\\\\themes\\\\friedensrat\",\"version\":\"1.0.0\",\"errors\":[{\"fatal\":true,\"level\":\"error\",\"rule\":\"Templates must contain valid Handlebars\",\"details\":\"Oops! You seemed to have used invalid Handlebars syntax. This mostly happens when you use a helper that is not supported.<br>See the full list of available helpers <a href=\\\"https://ghost.org/docs/themes/helpers/\\\" target=_blank>here</a>.\",\"failures\":[{\"ref\":\"index.hbs\",\"message\":\"Missing helper: \\\"add\\\"\"}],\"code\":\"GS005-TPL-ERR\"},{\"fatal\":false,\"level\":\"error\",\"rule\":\"The <code>{{#if author.*}}</code> block helper should be replaced with <code>{{#if primary_author.*}}</code>\\n        or <code>{{#if authors.[#].*}}</code>\",\"details\":\"The usage of <code>{{#if author.*}}</code> is no longer supported and should be replaced with <code>{{#if primary_author.*}}</code>or <code>{{#if authors.[#].*}}</code>.<br>Ghost allows multiple authors to be assigned to a post, so all helpers have been reworked to account for this.<br>Find more information about the <code>{{authors}}</code> helper <a href=\\\"https://ghost.org/docs/themes/helpers/authors/\\\" target=_blank>here</a>\",\"regex\":{},\"helper\":\"{{#if author.*}}\",\"failures\":[{\"ref\":\"author.hbs\",\"message\":\"Please remove or replace {{#if author.*}} from this template\"}],\"code\":\"GS001-DEPR-CON-AUTH\"},{\"fatal\":false,\"level\":\"error\",\"rule\":\"Replace <code>{{author.name}}</code> with <code>{{primary_author.name}}</code> or <code>{{authors.[#].name}}</code>\",\"details\":\"The usage of <code>{{author.name}}</code> is no longer supported and should be replaced with either <code>{{primary_author.name}}</code>or <code>{{authors.[#].name}}</code>.<br>Ghost allows multiple authors to be assigned to a post, so all helpers have been reworked to account for this.<br>Find more information about the <code>{{authors}}</code> helper <a href=\\\"https://ghost.org/docs/themes/helpers/authors/\\\" target=_blank>here</a>\",\"regex\":{},\"helper\":\"{{author.name}}\",\"failures\":[{\"ref\":\"author.hbs\",\"message\":\"Please remove or replace {{author.name}} from this template\"}],\"code\":\"GS001-DEPR-AUTH-NAME\"},{\"fatal\":false,\"level\":\"error\",\"rule\":\"Replace <code>{{author.bio}}</code> with <code>{{primary_author.bio}}</code> or <code>{{authors.[#].bio}}</code>\",\"details\":\"The usage of <code>{{author.bio}}</code> is no longer supported and should be replaced with either <code>{{primary_author.bio}}</code>or <code>{{authors.[#].bio}}</code>.<br>Ghost allows multiple authors to be assigned to a post, so all helpers have been reworked to account for this.<br>Find more information about the <code>{{authors}}</code> helper <a href=\\\"https://ghost.org/docs/themes/helpers/authors/\\\" target=_blank>here</a>\",\"regex\":{},\"helper\":\"{{author.bio}}\",\"failures\":[{\"ref\":\"author.hbs\",\"message\":\"Please remove or replace {{author.bio}} from this template\"}],\"code\":\"GS001-DEPR-AUTH-BIO\"},{\"fatal\":false,\"level\":\"error\",\"rule\":\"Replace <code>{{author.location}}</code> with <code>{{primary_author.location}}</code> or <code>{{authors.[#].location}}</code>\",\"details\":\"The usage of <code>{{author.location}}</code> is no longer supported and should be replaced with either <code>{{primary_author.location}}</code>or <code>{{authors.[#].location}}</code>.<br>Ghost allows multiple authors to be assigned to a post, so all helpers have been reworked to account for this.<br>Find more information about the <code>{{authors}}</code> helper <a href=\\\"https://ghost.org/docs/themes/helpers/authors/\\\" target=_blank>here</a>\",\"regex\":{},\"helper\":\"{{author.location}}\",\"failures\":[{\"ref\":\"author.hbs\",\"message\":\"Please remove or replace {{author.location}} from this template\"}],\"code\":\"GS001-DEPR-AUTH-LOC\"},{\"fatal\":false,\"level\":\"error\",\"rule\":\"Replace <code>{{author.website}}</code> with <code>{{primary_author.website}}</code> or <code>{{authors.[#].website}}</code>\",\"details\":\"The usage of <code>{{author.website}}</code> is no longer supported and should be replaced with either <code>{{primary_author.website}}</code>or <code>{{authors.[#].website}}</code>.<br>Ghost allows multiple authors to be assigned to a post, so all helpers have been reworked to account for this.<br>Find more information about the <code>{{authors}}</code> helper <a href=\\\"https://ghost.org/docs/themes/helpers/authors/\\\" target=_blank>here</a>\",\"regex\":{},\"helper\":\"{{author.website}}\",\"failures\":[{\"ref\":\"author.hbs\",\"message\":\"Please remove or replace {{author.website}} from this template\"}],\"code\":\"GS001-DEPR-AUTH-WEB\"},{\"fatal\":false,\"level\":\"error\",\"rule\":\"Replace <code>{{author.twitter}}</code> with <code>{{primary_author.twitter}}</code> or <code>{{authors.[#].twitter}}</code>\",\"details\":\"The usage of <code>{{author.twitter}}</code> is no longer supported and should be replaced with either <code>{{primary_author.twitter}}</code>or <code>{{authors.[#].twitter}}</code>.<br>Ghost allows multiple authors to be assigned to a post, so all helpers have been reworked to account for this.<br>Find more information about the <code>{{authors}}</code> helper <a href=\\\"https://ghost.org/docs/themes/helpers/authors/\\\" target=_blank>here</a>\",\"regex\":{},\"helper\":\"{{author.twitter}}\",\"failures\":[{\"ref\":\"author.hbs\",\"message\":\"Please remove or replace {{author.twitter}} from this template\"}],\"code\":\"GS001-DEPR-AUTH-TW\"},{\"fatal\":false,\"level\":\"error\",\"rule\":\"Replace <code>{{author.facebook}}</code> with <code>{{primary_author.facebook}}</code> or <code>{{authors.[#].facebook}}</code>\",\"details\":\"The usage of <code>{{author.facebook}}</code> is no longer supported and should be replaced with either <code>{{primary_author.facebook}}</code>or <code>{{authors.[#].facebook}}</code>.<br>Ghost allows multiple authors to be assigned to a post, so all helpers have been reworked to account for this.<br>Find more information about the <code>{{authors}}</code> helper <a href=\\\"https://ghost.org/docs/themes/helpers/authors/\\\" target=_blank>here</a>\",\"regex\":{},\"helper\":\"{{author.facebook}}\",\"failures\":[{\"ref\":\"author.hbs\",\"message\":\"Please remove or replace {{author.facebook}} from this template\"}],\"code\":\"GS001-DEPR-AUTH-FB\"},{\"fatal\":false,\"level\":\"error\",\"rule\":\"Replace <code>{{author.profile_image}}</code> with <code>{{primary_author.profile_image}}</code> or <code>{{authors.[#].profile_image}}</code>\",\"details\":\"The usage of <code>{{author.profile_image}}</code> is no longer supported and should be replaced with either <code>{{primary_author.profile_image}}</code>or <code>{{authors.[#].profile_image}}</code>.<br>Ghost allows multiple authors to be assigned to a post, so all helpers have been reworked to account for this.<br>Find more information about the <code>{{authors}}</code> helper <a href=\\\"https://ghost.org/docs/themes/helpers/authors/\\\" target=_blank>here</a>\",\"regex\":{},\"helper\":\"{{author.profile_image}}\",\"failures\":[{\"ref\":\"author.hbs\",\"message\":\"Please remove or replace {{author.profile_image}} from this template\"}],\"code\":\"GS001-DEPR-AUTH-PIMG\"},{\"fatal\":false,\"level\":\"error\",\"rule\":\"The <code>.kg-width-wide</code> CSS class is required to appear styled in your theme\",\"details\":\"The <code>.kg-width-wide</code> CSS class is required otherwise wide images will appear unstyled.Find out more about required theme changes for the Koenig editor <a href=\\\"https://ghost.org/docs/themes/content/\\\" target=_blank>here</a>.\",\"regex\":{},\"className\":\".kg-width-wide\",\"css\":true,\"failures\":[{\"ref\":\"styles\"}],\"code\":\"GS050-CSS-KGWW\"},{\"fatal\":false,\"level\":\"error\",\"rule\":\"The <code>.kg-width-full</code> CSS class is required to appear styled in your theme\",\"details\":\"The <code>.kg-width-full</code> CSS class is required otherwise wide images will appear unstyled.Find out more about required theme changes for the Koenig editor <a href=\\\"https://ghost.org/docs/themes/content/\\\" target=_blank>here</a>.\",\"regex\":{},\"className\":\".kg-width-full\",\"css\":true,\"failures\":[{\"ref\":\"styles\"}],\"code\":\"GS050-CSS-KGWF\"},{\"fatal\":false,\"level\":\"error\",\"rule\":\"Not all page features are being used\",\"details\":\"<b>This error only applies to pages created with the Beta editor.</b> Some page features used by Ghost via the <code>{{@page}}</code> global are not implemented in this theme.&nbsp;Find more information about the <code>{{@page}}</code> global <a href=\\\"https://ghost.org/docs/themes/helpers/page/\\\" target=_blank>here</a>.\",\"failures\":[{\"ref\":\"page.hbs\",\"message\":\"@page.show_title_and_feature_image is not used\",\"rule\":\"GS110-NO-MISSING-PAGE-BUILDER-USAGE\"}],\"code\":\"GS110-NO-MISSING-PAGE-BUILDER-USAGE\"}],\"warnings\":[{\"fatal\":false,\"level\":\"warning\",\"rule\":\"Missing support for custom fonts\",\"details\":\"CSS variables for Ghost font settings are not present: <code>--gh-font-heading</code>, <code>--gh-font-body</code>\",\"regex\":{},\"failures\":[{\"ref\":\"styles\"}],\"code\":\"GS051-CUSTOM-FONTS\"}]}"},"msg":"Theme \"friedensrat\" is not compatible or contains errors.","time":"2025-05-04T09:29:28.617Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":4596,"level":50,"version":"5.115.1","req":{"meta":{"requestId":"bffbdfb7-93b2-4e7e-b3a2-afb26a37f146","userId":"1"},"url":"/themes/friedensrat/activate/","method":"PUT","originalUrl":"/ghost/api/admin/themes/friedensrat/activate/","params":{},"headers":{"host":"localhost:2368","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0","accept":"*/*","accept-language":"de,en-US;q=0.7,en;q=0.3","accept-encoding":"gzip, deflate, br, zstd","referer":"http://localhost:2368/ghost/","app-pragma":"no-cache","x-ghost-version":"5.115","origin":"http://localhost:2368","connection":"keep-alive","cookie":"**REDACTED**","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","priority":"u=0","content-length":"0"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.115","vary":"Accept-Version, Origin, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"http://localhost:2368","content-type":"application/json; charset=utf-8","etag":"W/\"43d-emqz0msAm8qib7+FPWjeZq+xBTU\"","content-encoding":"br"},"statusCode":422,"responseTime":"117ms"},"err":{"id":"ff9ab840-28ca-11f0-979b-a5e841421b16","domain":"http://localhost:2368/","code":null,"name":"ThemeValidationError","statusCode":422,"level":"normal","message":"Theme \"friedensrat\" is not compatible or contains errors.","stack":"ThemeValidationError: Theme \"friedensrat\" is not compatible or contains errors.\n    at getThemeValidationError (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\themes\\validate.js:144:12)\n    at Object.checkSafe (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\themes\\validate.js:140:11)\n    at async module.exports.activate (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\themes\\activate.js:57:26)\n    at async Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\themes.js:62:33)\n    at async getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:34)\n    at async ImplWrapper (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:264:30)\n    at async Http (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\http.js:70:28)","hideStack":false,"errorDetails":"{\"checkedVersion\":\"5.x\",\"name\":\"friedensrat\",\"path\":\"C:\\\\ghost_test\\\\content\\\\themes\\\\friedensrat\",\"version\":\"1.0.0\",\"errors\":[{\"fatal\":true,\"level\":\"error\",\"rule\":\"Templates must contain valid Handlebars\",\"details\":\"Oops! You seemed to have used invalid Handlebars syntax. This mostly happens when you use a helper that is not supported.<br>See the full list of available helpers <a href=\\\"https://ghost.org/docs/themes/helpers/\\\" target=_blank>here</a>.\",\"failures\":[{\"ref\":\"index.hbs\",\"message\":\"Missing helper: \\\"math\\\"\"}],\"code\":\"GS005-TPL-ERR\"}],\"warnings\":[{\"fatal\":false,\"level\":\"warning\",\"rule\":\"Missing support for custom fonts\",\"details\":\"CSS variables for Ghost font settings are not present: <code>--gh-font-heading</code>, <code>--gh-font-body</code>\",\"regex\":{},\"failures\":[{\"ref\":\"styles\"}],\"code\":\"GS051-CUSTOM-FONTS\"}]}"},"msg":"Theme \"friedensrat\" is not compatible or contains errors.","time":"2025-05-04T09:34:38.539Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":11036,"level":50,"version":"5.115.1","err":{"id":"eb0aefc0-28cb-11f0-b96d-39c7768f87ff","domain":"http://localhost:2368/","code":null,"name":"BadRequestError","statusCode":400,"level":"normal","message":"Error parsing filter","stack":"Error: Query Error: unexpected character in filter at char 5\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:68:23)\n(id:-+tag:)+type:post\n-----^\nExpecting 'LBRACKET', 'NULL', 'TRUE', 'FALSE', 'NUMBER', 'NOW', 'LITERAL', 'STRING', 'CONTAINS', 'STARTSWITH', 'ENDSWITH', got 'AND'\n    at parser.parseError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:359:12)\n    at Parser.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:276:22)\n    at exports.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\lib\\nql.js:18:44)\n    at api.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:33:31)\n    at api.querySQL (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:77:44)\n    at QueryBuilder_SQLite3.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:65:24)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\helpers.js:164:14)\n    at Child.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\model.js:1387:22)\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:57:22)\n    at Function.getFilteredCollection (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\filtered-collection.js:13:32)\n    at Function.findPage (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\crud.js:83:41)\n    at PostsService.browsePosts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\posts-service\\lib\\PostsService.js:43:46)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\posts-public.js:116:33)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:159:24)\n    at getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Error parsing filter","time":"2025-05-04T09:41:13.533Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":19420,"level":50,"version":"5.115.1","err":{"id":"4edda280-28cd-11f0-84d6-732294857c3c","domain":"http://localhost:2368/","code":null,"name":"ThemeValidationError","statusCode":422,"level":"normal","message":"The currently active theme \"friedensrat\" has fatal errors.","stack":"ThemeValidationError: The currently active theme \"friedensrat\" has fatal errors.\n    at Object.getThemeValidationError (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\themes\\validate.js:144:12)\n    at module.exports.loadAndActivate (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\themes\\activate.js:27:36)\n    at async initServicesForFrontend (C:\\ghost_test\\versions\\5.115.1\\core\\boot.js:186:5)\n    at async bootGhost (C:\\ghost_test\\versions\\5.115.1\\core\\boot.js:570:31)","hideStack":false,"errorDetails":"{\"checkedVersion\":\"5.x\",\"name\":\"friedensrat\",\"path\":\"C:\\\\ghost_test\\\\content\\\\themes\\\\friedensrat\",\"version\":\"1.0.0\",\"errors\":[{\"fatal\":true,\"level\":\"error\",\"rule\":\"The <code>{{image}}</code> helper was replaced with the <code>{{img_url}}</code> helper.</code>.\",\"details\":\"The <code>{{image}}</code> helper was replaced with the <code>{{img_url}}</code> helper.<br>Depending on the context of the <code>{{img_url}}</code> helper you would need to use e. g. <br><br><code>{{#post}}<br>&nbsp;&nbsp;&nbsp;&nbsp;{{img_url feature_image}}<br>{{/post}}</code><br><br>to render the feature image of the blog post.<br><br><b>If you are using <code>{{if image}}</code></b>, then you have to replace it with e.g. <code>{{if feature_image}}.</code><br><br>Find more information about the <code>{{img_url}}</code> helper <a href=\\\"https://ghost.org/docs/themes/helpers/img_url/\\\" target=_blank>here</a> andread more about Ghost's usage of contexts <a href=\\\"https://ghost.org/docs/themes/contexts/\\\" target=_blank>here</a>.\",\"regex\":{},\"helper\":\"{{image}}\",\"failures\":[{\"ref\":\"index.hbs\",\"message\":\"Please remove or replace {{image}} from this template\"}],\"code\":\"GS001-DEPR-IMG\"},{\"fatal\":true,\"level\":\"error\",\"rule\":\"Replace <code>{{#if image}}</code> with <code>{{#if feature_image}}</code>, or <code>{{#if profile_image}}</code>\",\"details\":\"The <code>image</code> attribute was replaced with <code>feature_image</code> and <code>profile_image</code>.<br>Depending on the <a href=\\\"https://ghost.org/docs/themes/contexts/\\\" target=_blank>context</a> you will need to replace it like this:<br><br><code>{{#author}}<br>&nbsp;&nbsp;&nbsp;&nbsp;{{#if profile_image}}<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{profile_image}}<br>&nbsp;&nbsp;&nbsp;&nbsp;{{/if}}<br>{{/author}}</code><br><br>See the object attributes of <code>author</code> <a href=\\\"https://ghost.org/docs/themes/contexts/author/#author-object-attributes\\\" target=_blank>here</a>.<br><br><code>{{#post}}<br>&nbsp;&nbsp;&nbsp;&nbsp;{{#if feature_image}}<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{feature_image}}<br>&nbsp;&nbsp;&nbsp;&nbsp;{{/if}}<br>{{/post}}</code><br><br>See the object attributes of <code>post</code> <a href=\\\"https://ghost.org/docs/themes/contexts/post/#post-object-attributes\\\" target=_blank>here</a>.<br><br><code>{{#tag}}<br>&nbsp;&nbsp;&nbsp;&nbsp;{{#if feature_image}}<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{feature_image}}<br>&nbsp;&nbsp;&nbsp;&nbsp;{{/if}}<br>{{/tag}}</code><br><br>See the object attributes of <code>tags</code> <a href=\\\"https://ghost.org/docs/themes/contexts/tag/#tag-object-attributes\\\" target=_blank>here</a>.\",\"regex\":{},\"helper\":\"{{#if image}}\",\"failures\":[{\"ref\":\"index.hbs\",\"message\":\"Please remove or replace {{#if image}} from this template\"}],\"code\":\"GS001-DEPR-CON-IMG\"},{\"fatal\":false,\"level\":\"error\",\"rule\":\"An unknown custom theme setting has been used.\",\"details\":\"The custom theme setting should all be defined in the package.json <code>config.custom</code> object.\",\"failures\":[{\"ref\":\"index.hbs\",\"message\":\"Missing Custom Theme Setting: \\\"teamMembers\\\"\"}],\"code\":\"GS090-NO-UNKNOWN-CUSTOM-THEME-SETTINGS\"}],\"warnings\":[{\"fatal\":false,\"level\":\"warning\",\"rule\":\"Missing support for custom fonts\",\"details\":\"CSS variables for Ghost font settings are not present: <code>--gh-font-heading</code>, <code>--gh-font-body</code>\",\"regex\":{},\"failures\":[{\"ref\":\"styles\"}],\"code\":\"GS051-CUSTOM-FONTS\"}]}"},"msg":"The currently active theme \"friedensrat\" has fatal errors.","time":"2025-05-04T09:51:10.506Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":8300,"level":50,"version":"5.115.1","err":{"id":"30934fe0-28ce-11f0-9bc3-838bbcdf96c8","domain":"http://localhost:2368/","code":null,"name":"BadRequestError","statusCode":400,"level":"normal","message":"Error parsing filter","stack":"Error: Query Error: unexpected character in filter at char 2\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:68:23)\n(tag:)+type:post\n-----^\nExpecting 'NOT', 'LBRACKET', 'NULL', 'TRUE', 'FALSE', 'NUMBER', 'NOW', 'LITERAL', 'STRING', 'CONTAINS', 'STARTSWITH', 'ENDSWITH', 'GT', 'LT', 'GTE', 'LTE', got 'RPAREN'\n    at parser.parseError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:359:12)\n    at Parser.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:276:22)\n    at exports.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\lib\\nql.js:18:44)\n    at api.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:33:31)\n    at api.querySQL (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:77:44)\n    at QueryBuilder_SQLite3.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:65:24)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\helpers.js:164:14)\n    at Child.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\model.js:1387:22)\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:57:22)\n    at Function.getFilteredCollection (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\filtered-collection.js:13:32)\n    at Function.findPage (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\crud.js:83:41)\n    at PostsService.browsePosts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\posts-service\\lib\\PostsService.js:43:46)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\posts-public.js:116:33)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:159:24)\n    at getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Error parsing filter","time":"2025-05-04T09:57:29.184Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":8300,"level":50,"version":"5.115.1","err":{"id":"3c03ee70-28ce-11f0-9bc3-838bbcdf96c8","domain":"http://localhost:2368/","code":null,"name":"BadRequestError","statusCode":400,"level":"normal","message":"Error parsing filter","stack":"Error: Query Error: unexpected character in filter at char 2\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:68:23)\n(tag:)+type:post\n-----^\nExpecting 'NOT', 'LBRACKET', 'NULL', 'TRUE', 'FALSE', 'NUMBER', 'NOW', 'LITERAL', 'STRING', 'CONTAINS', 'STARTSWITH', 'ENDSWITH', 'GT', 'LT', 'GTE', 'LTE', got 'RPAREN'\n    at parser.parseError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:359:12)\n    at Parser.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:276:22)\n    at exports.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\lib\\nql.js:18:44)\n    at api.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:33:31)\n    at api.querySQL (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:77:44)\n    at QueryBuilder_SQLite3.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:65:24)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\helpers.js:164:14)\n    at Child.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\model.js:1387:22)\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:57:22)\n    at Function.getFilteredCollection (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\filtered-collection.js:13:32)\n    at Function.findPage (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\crud.js:83:41)\n    at PostsService.browsePosts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\posts-service\\lib\\PostsService.js:43:46)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\posts-public.js:116:33)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:159:24)\n    at getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Error parsing filter","time":"2025-05-04T09:57:48.375Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":8300,"level":50,"version":"5.115.1","err":{"id":"47732d70-28ce-11f0-9bc3-838bbcdf96c8","domain":"http://localhost:2368/","code":null,"name":"BadRequestError","statusCode":400,"level":"normal","message":"Error parsing filter","stack":"Error: Query Error: unexpected character in filter at char 5\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:68:23)\n(id:-+tag:)+type:post\n-----^\nExpecting 'LBRACKET', 'NULL', 'TRUE', 'FALSE', 'NUMBER', 'NOW', 'LITERAL', 'STRING', 'CONTAINS', 'STARTSWITH', 'ENDSWITH', got 'AND'\n    at parser.parseError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:359:12)\n    at Parser.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:276:22)\n    at exports.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\lib\\nql.js:18:44)\n    at api.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:33:31)\n    at api.querySQL (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:77:44)\n    at QueryBuilder_SQLite3.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:65:24)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\helpers.js:164:14)\n    at Child.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\model.js:1387:22)\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:57:22)\n    at Function.getFilteredCollection (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\filtered-collection.js:13:32)\n    at Function.findPage (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\crud.js:83:41)\n    at PostsService.browsePosts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\posts-service\\lib\\PostsService.js:43:46)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\posts-public.js:116:33)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:159:24)\n    at getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Error parsing filter","time":"2025-05-04T09:58:07.560Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":8300,"level":50,"version":"5.115.1","err":{"id":"47d3c400-28ce-11f0-9bc3-838bbcdf96c8","domain":"http://localhost:2368/","code":null,"name":"BadRequestError","statusCode":400,"level":"normal","message":"Error parsing filter","stack":"Error: Query Error: unexpected character in filter at char 2\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:68:23)\n(tag:)+type:post\n-----^\nExpecting 'NOT', 'LBRACKET', 'NULL', 'TRUE', 'FALSE', 'NUMBER', 'NOW', 'LITERAL', 'STRING', 'CONTAINS', 'STARTSWITH', 'ENDSWITH', 'GT', 'LT', 'GTE', 'LTE', got 'RPAREN'\n    at parser.parseError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:359:12)\n    at Parser.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:276:22)\n    at exports.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\lib\\nql.js:18:44)\n    at api.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:33:31)\n    at api.querySQL (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:77:44)\n    at QueryBuilder_SQLite3.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:65:24)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\helpers.js:164:14)\n    at Child.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\model.js:1387:22)\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:57:22)\n    at Function.getFilteredCollection (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\filtered-collection.js:13:32)\n    at Function.findPage (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\crud.js:83:41)\n    at PostsService.browsePosts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\posts-service\\lib\\PostsService.js:43:46)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\posts-public.js:116:33)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:159:24)\n    at getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Error parsing filter","time":"2025-05-04T09:58:08.192Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":8300,"level":50,"version":"5.115.1","err":{"id":"492ac880-28ce-11f0-9bc3-838bbcdf96c8","domain":"http://localhost:2368/","code":null,"name":"BadRequestError","statusCode":400,"level":"normal","message":"Error parsing filter","stack":"Error: Query Error: unexpected character in filter at char 2\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:68:23)\n(tag:)+type:post\n-----^\nExpecting 'NOT', 'LBRACKET', 'NULL', 'TRUE', 'FALSE', 'NUMBER', 'NOW', 'LITERAL', 'STRING', 'CONTAINS', 'STARTSWITH', 'ENDSWITH', 'GT', 'LT', 'GTE', 'LTE', got 'RPAREN'\n    at parser.parseError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:359:12)\n    at Parser.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:276:22)\n    at exports.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\lib\\nql.js:18:44)\n    at api.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:33:31)\n    at api.querySQL (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:77:44)\n    at QueryBuilder_SQLite3.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:65:24)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\helpers.js:164:14)\n    at Child.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\model.js:1387:22)\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:57:22)\n    at Function.getFilteredCollection (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\filtered-collection.js:13:32)\n    at Function.findPage (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\crud.js:83:41)\n    at PostsService.browsePosts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\posts-service\\lib\\PostsService.js:43:46)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\posts-public.js:116:33)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:159:24)\n    at getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Error parsing filter","time":"2025-05-04T09:58:10.440Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":8300,"level":50,"version":"5.115.1","err":{"id":"4a129070-28ce-11f0-9bc3-838bbcdf96c8","domain":"http://localhost:2368/","code":null,"name":"BadRequestError","statusCode":400,"level":"normal","message":"Error parsing filter","stack":"Error: Query Error: unexpected character in filter at char 2\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:68:23)\n(tag:)+type:post\n-----^\nExpecting 'NOT', 'LBRACKET', 'NULL', 'TRUE', 'FALSE', 'NUMBER', 'NOW', 'LITERAL', 'STRING', 'CONTAINS', 'STARTSWITH', 'ENDSWITH', 'GT', 'LT', 'GTE', 'LTE', got 'RPAREN'\n    at parser.parseError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:359:12)\n    at Parser.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:276:22)\n    at exports.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\lib\\nql.js:18:44)\n    at api.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:33:31)\n    at api.querySQL (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:77:44)\n    at QueryBuilder_SQLite3.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:65:24)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\helpers.js:164:14)\n    at Child.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\model.js:1387:22)\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:57:22)\n    at Function.getFilteredCollection (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\filtered-collection.js:13:32)\n    at Function.findPage (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\crud.js:83:41)\n    at PostsService.browsePosts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\posts-service\\lib\\PostsService.js:43:46)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\posts-public.js:116:33)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:159:24)\n    at getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Error parsing filter","time":"2025-05-04T09:58:11.960Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":8300,"level":50,"version":"5.115.1","err":{"id":"4abd2850-28ce-11f0-9bc3-838bbcdf96c8","domain":"http://localhost:2368/","code":null,"name":"BadRequestError","statusCode":400,"level":"normal","message":"Error parsing filter","stack":"Error: Query Error: unexpected character in filter at char 2\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:68:23)\n(tag:)+type:post\n-----^\nExpecting 'NOT', 'LBRACKET', 'NULL', 'TRUE', 'FALSE', 'NUMBER', 'NOW', 'LITERAL', 'STRING', 'CONTAINS', 'STARTSWITH', 'ENDSWITH', 'GT', 'LT', 'GTE', 'LTE', got 'RPAREN'\n    at parser.parseError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:359:12)\n    at Parser.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:276:22)\n    at exports.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\lib\\nql.js:18:44)\n    at api.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:33:31)\n    at api.querySQL (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:77:44)\n    at QueryBuilder_SQLite3.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:65:24)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\helpers.js:164:14)\n    at Child.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\model.js:1387:22)\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:57:22)\n    at Function.getFilteredCollection (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\filtered-collection.js:13:32)\n    at Function.findPage (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\crud.js:83:41)\n    at PostsService.browsePosts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\posts-service\\lib\\PostsService.js:43:46)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\posts-public.js:116:33)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:159:24)\n    at getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Error parsing filter","time":"2025-05-04T09:58:13.078Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":8300,"level":50,"version":"5.115.1","err":{"id":"4c8ae370-28ce-11f0-9bc3-838bbcdf96c8","domain":"http://localhost:2368/","code":null,"name":"BadRequestError","statusCode":400,"level":"normal","message":"Error parsing filter","stack":"Error: Query Error: unexpected character in filter at char 2\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:68:23)\n(tag:)+type:post\n-----^\nExpecting 'NOT', 'LBRACKET', 'NULL', 'TRUE', 'FALSE', 'NUMBER', 'NOW', 'LITERAL', 'STRING', 'CONTAINS', 'STARTSWITH', 'ENDSWITH', 'GT', 'LT', 'GTE', 'LTE', got 'RPAREN'\n    at parser.parseError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:359:12)\n    at Parser.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:276:22)\n    at exports.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\lib\\nql.js:18:44)\n    at api.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:33:31)\n    at api.querySQL (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:77:44)\n    at QueryBuilder_SQLite3.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:65:24)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\helpers.js:164:14)\n    at Child.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\model.js:1387:22)\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:57:22)\n    at Function.getFilteredCollection (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\filtered-collection.js:13:32)\n    at Function.findPage (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\crud.js:83:41)\n    at PostsService.browsePosts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\posts-service\\lib\\PostsService.js:43:46)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\posts-public.js:116:33)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:159:24)\n    at getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Error parsing filter","time":"2025-05-04T09:58:16.104Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":8300,"level":50,"version":"5.115.1","err":{"id":"76a6c3e0-28ce-11f0-9bc3-838bbcdf96c8","domain":"http://localhost:2368/","code":null,"name":"BadRequestError","statusCode":400,"level":"normal","message":"Error parsing filter","stack":"Error: Query Error: unexpected character in filter at char 2\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:68:23)\n(tag:)+type:post\n-----^\nExpecting 'NOT', 'LBRACKET', 'NULL', 'TRUE', 'FALSE', 'NUMBER', 'NOW', 'LITERAL', 'STRING', 'CONTAINS', 'STARTSWITH', 'ENDSWITH', 'GT', 'LT', 'GTE', 'LTE', got 'RPAREN'\n    at parser.parseError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:359:12)\n    at Parser.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:276:22)\n    at exports.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\lib\\nql.js:18:44)\n    at api.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:33:31)\n    at api.querySQL (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:77:44)\n    at QueryBuilder_SQLite3.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:65:24)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\helpers.js:164:14)\n    at Child.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\model.js:1387:22)\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:57:22)\n    at Function.getFilteredCollection (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\filtered-collection.js:13:32)\n    at Function.findPage (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\crud.js:83:41)\n    at PostsService.browsePosts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\posts-service\\lib\\PostsService.js:43:46)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\posts-public.js:116:33)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:159:24)\n    at getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Error parsing filter","time":"2025-05-04T09:59:26.751Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":8300,"level":50,"version":"5.115.1","err":{"id":"add50c00-28ce-11f0-9bc3-838bbcdf96c8","domain":"http://localhost:2368/","code":null,"name":"BadRequestError","statusCode":400,"level":"normal","message":"Error parsing filter","stack":"Error: Query Error: unexpected character in filter at char 2\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:68:23)\n(tag:)+type:post\n-----^\nExpecting 'NOT', 'LBRACKET', 'NULL', 'TRUE', 'FALSE', 'NUMBER', 'NOW', 'LITERAL', 'STRING', 'CONTAINS', 'STARTSWITH', 'ENDSWITH', 'GT', 'LT', 'GTE', 'LTE', got 'RPAREN'\n    at parser.parseError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:359:12)\n    at Parser.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:276:22)\n    at exports.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\lib\\nql.js:18:44)\n    at api.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:33:31)\n    at api.querySQL (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:77:44)\n    at QueryBuilder_SQLite3.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:65:24)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\helpers.js:164:14)\n    at Child.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\model.js:1387:22)\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:57:22)\n    at Function.getFilteredCollection (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\filtered-collection.js:13:32)\n    at Function.findPage (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\crud.js:83:41)\n    at PostsService.browsePosts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\posts-service\\lib\\PostsService.js:43:46)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\posts-public.js:116:33)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:159:24)\n    at getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Error parsing filter","time":"2025-05-04T10:00:59.329Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":10028,"level":50,"version":"5.115.1","req":{"meta":{"requestId":"c2ac576f-7375-4584-ab21-1c854f4f721b","userId":null},"url":"/","method":"GET","originalUrl":"/","params":{},"headers":{"host":"localhost:2368","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"de,en-US;q=0.7,en;q=0.3","accept-encoding":"gzip, deflate, br, zstd","connection":"keep-alive","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"none","sec-fetch-user":"?1","if-none-match":"W/\"4f9a-PJOVDSnYZWfTFEvF+xMPEMHg0SY\"","priority":"u=0, i"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"text/html; charset=utf-8","etag":"W/\"617-pBOz0GJW+wRijGojcp7S+61eKK8\"","vary":"Accept-Encoding","content-encoding":"br"},"statusCode":500,"responseTime":"569ms"},"err":{"id":"2122fe70-2aa4-11f0-912c-05c79d21cf6a","domain":"http://localhost:2368/","code":"UNEXPECTED_ERROR","name":"InternalServerError","statusCode":500,"level":"critical","message":"An unexpected error occurred, please try again.","context":"\"[index.hbs] options.fn is not a function\"","stack":"TypeError: [index.hbs] options.fn is not a function\n    at prepareError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\mw-error-handler\\lib\\mw-error-handler.js:113:19)\n    at Object.has (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\has.js:172:24)\n    at Object.wrapper (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\internal\\wrapHelper.js:15:19)\n    at eval (eval at createFunctionContext (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\javascript-compiler.js:262:23), <anonymous>:15:145)\n    at prog (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:268:12)\n    at execIteration (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:88:27)\n    at C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:107:17\n    at arrayEach (C:\\ghost_test\\versions\\5.115.1\\node_modules\\lodash\\lodash.js:530:11)\n    at Function.forEach (C:\\ghost_test\\versions\\5.115.1\\node_modules\\lodash\\lodash.js:9410:14)\n    at iterateCollection (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:100:11)\n    at Object.foreach (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:115:9)\n    at Object.wrapper (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\internal\\wrapHelper.js:15:19)\n    at Object.eval [as main] (eval at createFunctionContext (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\javascript-compiler.js:262:23), <anonymous>:11:108)\n    at main (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:208:32)\n    at ret (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:212:12)\n    at ret (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\compiler.js:519:21)\n    at renderTemplate (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:499:13)","hideStack":false},"msg":"An unexpected error occurred, please try again.","time":"2025-05-06T18:01:26.761Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":10028,"level":50,"version":"5.115.1","req":{"meta":{"requestId":"06e7b6db-dd77-4995-beaf-8a07ea794dab","userId":null},"url":"/","method":"GET","originalUrl":"/","params":{},"headers":{"host":"localhost:2368","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"de,en-US;q=0.7,en;q=0.3","accept-encoding":"gzip, deflate, br, zstd","connection":"keep-alive","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","priority":"u=0, i"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"text/html; charset=utf-8","etag":"W/\"617-pBOz0GJW+wRijGojcp7S+61eKK8\"","vary":"Accept-Encoding","content-encoding":"br"},"statusCode":500,"responseTime":"120ms"},"err":{"id":"2e133820-2aa4-11f0-912c-05c79d21cf6a","domain":"http://localhost:2368/","code":"UNEXPECTED_ERROR","name":"InternalServerError","statusCode":500,"level":"critical","message":"An unexpected error occurred, please try again.","context":"\"[index.hbs] options.fn is not a function\"","stack":"TypeError: [index.hbs] options.fn is not a function\n    at prepareError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\mw-error-handler\\lib\\mw-error-handler.js:113:19)\n    at Object.has (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\has.js:172:24)\n    at Object.wrapper (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\internal\\wrapHelper.js:15:19)\n    at eval (eval at createFunctionContext (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\javascript-compiler.js:262:23), <anonymous>:15:145)\n    at prog (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:268:12)\n    at execIteration (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:88:27)\n    at C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:107:17\n    at arrayEach (C:\\ghost_test\\versions\\5.115.1\\node_modules\\lodash\\lodash.js:530:11)\n    at Function.forEach (C:\\ghost_test\\versions\\5.115.1\\node_modules\\lodash\\lodash.js:9410:14)\n    at iterateCollection (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:100:11)\n    at Object.foreach (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:115:9)\n    at Object.wrapper (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\internal\\wrapHelper.js:15:19)\n    at Object.eval [as main] (eval at createFunctionContext (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\javascript-compiler.js:262:23), <anonymous>:11:108)\n    at main (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:208:32)\n    at ret (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:212:12)\n    at ret (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\compiler.js:519:21)\n    at renderTemplate (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:499:13)","hideStack":false},"msg":"An unexpected error occurred, please try again.","time":"2025-05-06T18:01:48.504Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":18932,"level":50,"version":"5.115.1","err":{"id":"e691e6d0-2aa4-11f0-8996-b505e684a03a","domain":"http://localhost:2368/","code":null,"name":"BadRequestError","statusCode":400,"level":"normal","message":"Error parsing filter","stack":"Error: Query Error: unexpected character in filter at char 5\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:68:23)\n(id:-+tag:)+type:post\n-----^\nExpecting 'LBRACKET', 'NULL', 'TRUE', 'FALSE', 'NUMBER', 'NOW', 'LITERAL', 'STRING', 'CONTAINS', 'STARTSWITH', 'ENDSWITH', got 'AND'\n    at parser.parseError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:359:12)\n    at Parser.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:276:22)\n    at exports.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\lib\\nql.js:18:44)\n    at api.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:33:31)\n    at api.querySQL (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:77:44)\n    at QueryBuilder_SQLite3.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:65:24)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\helpers.js:164:14)\n    at Child.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\model.js:1387:22)\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:57:22)\n    at Function.getFilteredCollection (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\filtered-collection.js:13:32)\n    at Function.findPage (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\crud.js:83:41)\n    at PostsService.browsePosts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\posts-service\\lib\\PostsService.js:43:46)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\posts-public.js:116:33)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:159:24)\n    at getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Error parsing filter","time":"2025-05-06T18:06:57.982Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":18916,"level":50,"version":"5.115.1","err":{"id":"e6628fe0-393e-11f0-a3b8-c77c6e49eaf6","domain":"http://localhost:2368/","code":null,"name":"BadRequestError","statusCode":400,"level":"normal","message":"Error parsing filter","stack":"Error: Query Error: unexpected character in filter at char 5\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:68:23)\n(id:-+tag:)+type:post\n-----^\nExpecting 'LBRACKET', 'NULL', 'TRUE', 'FALSE', 'NUMBER', 'NOW', 'LITERAL', 'STRING', 'CONTAINS', 'STARTSWITH', 'ENDSWITH', got 'AND'\n    at parser.parseError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:359:12)\n    at Parser.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:276:22)\n    at exports.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\lib\\nql.js:18:44)\n    at api.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:33:31)\n    at api.querySQL (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:77:44)\n    at QueryBuilder_SQLite3.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:65:24)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\helpers.js:164:14)\n    at Child.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\model.js:1387:22)\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:57:22)\n    at Function.getFilteredCollection (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\filtered-collection.js:13:32)\n    at Function.findPage (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\crud.js:83:41)\n    at PostsService.browsePosts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\posts-service\\lib\\PostsService.js:43:46)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\posts-public.js:116:33)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:159:24)\n    at getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Error parsing filter","time":"2025-05-25T08:04:36.447Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":18916,"level":50,"version":"5.115.1","err":{"id":"b1a008e0-3944-11f0-a3b8-c77c6e49eaf6","domain":"http://localhost:2368/","code":null,"name":"BadRequestError","statusCode":400,"level":"normal","message":"Error parsing filter","stack":"Error: Query Error: unexpected character in filter at char 5\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:68:23)\n(id:-+tag:)+type:post\n-----^\nExpecting 'LBRACKET', 'NULL', 'TRUE', 'FALSE', 'NUMBER', 'NOW', 'LITERAL', 'STRING', 'CONTAINS', 'STARTSWITH', 'ENDSWITH', got 'AND'\n    at parser.parseError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:359:12)\n    at Parser.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:276:22)\n    at exports.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\lib\\nql.js:18:44)\n    at api.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:33:31)\n    at api.querySQL (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:77:44)\n    at QueryBuilder_SQLite3.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:65:24)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\helpers.js:164:14)\n    at Child.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\model.js:1387:22)\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:57:22)\n    at Function.getFilteredCollection (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\filtered-collection.js:13:32)\n    at Function.findPage (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\crud.js:83:41)\n    at PostsService.browsePosts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\posts-service\\lib\\PostsService.js:43:46)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\posts-public.js:116:33)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:159:24)\n    at getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Error parsing filter","time":"2025-05-25T08:46:04.910Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":18916,"level":50,"version":"5.115.1","err":{"id":"355e1ea0-3946-11f0-a3b8-c77c6e49eaf6","domain":"http://localhost:2368/","code":null,"name":"BadRequestError","statusCode":400,"level":"normal","message":"Error parsing filter","stack":"Error: Query Error: unexpected character in filter at char 5\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:68:23)\n(id:-+tag:)+type:post\n-----^\nExpecting 'LBRACKET', 'NULL', 'TRUE', 'FALSE', 'NUMBER', 'NOW', 'LITERAL', 'STRING', 'CONTAINS', 'STARTSWITH', 'ENDSWITH', got 'AND'\n    at parser.parseError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:359:12)\n    at Parser.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:276:22)\n    at exports.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\lib\\nql.js:18:44)\n    at api.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:33:31)\n    at api.querySQL (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:77:44)\n    at QueryBuilder_SQLite3.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:65:24)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\helpers.js:164:14)\n    at Child.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\model.js:1387:22)\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:57:22)\n    at Function.getFilteredCollection (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\filtered-collection.js:13:32)\n    at Function.findPage (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\crud.js:83:41)\n    at PostsService.browsePosts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\posts-service\\lib\\PostsService.js:43:46)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\posts-public.js:116:33)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:159:24)\n    at getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Error parsing filter","time":"2025-05-25T08:56:55.435Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":18916,"level":50,"version":"5.115.1","err":{"domain":"http://localhost:2368/","message":"#unless requires exactly one argument","stack":"Error: #unless requires exactly one argument\n    at Object.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\helpers\\if.js:35:13)\n    at Object.wrapper (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\internal\\wrapHelper.js:15:19)\n    at eval (eval at createFunctionContext (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\javascript-compiler.js:262:23), <anonymous>:10:54)\n    at prog (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:268:12)\n    at execIteration (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:88:27)\n    at C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:107:17\n    at arrayEach (C:\\ghost_test\\versions\\5.115.1\\node_modules\\lodash\\lodash.js:530:11)\n    at Function.forEach (C:\\ghost_test\\versions\\5.115.1\\node_modules\\lodash\\lodash.js:9410:14)\n    at iterateCollection (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:100:11)\n    at Object.foreach (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:115:9)\n    at Object.wrapper (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\internal\\wrapHelper.js:15:19)\n    at eval (eval at createFunctionContext (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\javascript-compiler.js:262:23), <anonymous>:10:134)\n    at Object.prog [as fn] (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:268:12)\n    at Object.get (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\get.js:315:34)\n    at async Object.returnAsync (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\services\\helpers\\handlebars.js:16:30)"},"msg":"#unless requires exactly one argument","time":"2025-05-25T08:57:49.541Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":18916,"level":50,"version":"5.115.1","err":{"id":"5de86290-3946-11f0-a3b8-c77c6e49eaf6","domain":"http://localhost:2368/","code":null,"name":"BadRequestError","statusCode":400,"level":"normal","message":"Error parsing filter","stack":"Error: Query Error: unexpected character in filter at char 5\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:68:23)\n(id:-+tag:)+type:post\n-----^\nExpecting 'LBRACKET', 'NULL', 'TRUE', 'FALSE', 'NUMBER', 'NOW', 'LITERAL', 'STRING', 'CONTAINS', 'STARTSWITH', 'ENDSWITH', got 'AND'\n    at parser.parseError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:359:12)\n    at Parser.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\dist\\parser.js:276:22)\n    at exports.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\nql-lang\\lib\\nql.js:18:44)\n    at api.parse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:33:31)\n    at api.querySQL (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\node_modules\\@tryghost\\nql\\lib\\nql.js:77:44)\n    at QueryBuilder_SQLite3.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:65:24)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\helpers.js:164:14)\n    at Child.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\bookshelf\\lib\\model.js:1387:22)\n    at Child.applyDefaultAndCustomFilters (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\bookshelf-filter\\lib\\bookshelf-filter.js:57:22)\n    at Function.getFilteredCollection (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\filtered-collection.js:13:32)\n    at Function.findPage (C:\\ghost_test\\versions\\5.115.1\\core\\server\\models\\base\\plugins\\crud.js:83:41)\n    at PostsService.browsePosts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\posts-service\\lib\\PostsService.js:43:46)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\core\\server\\api\\endpoints\\posts-public.js:116:33)\n    at Object.query (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:159:24)\n    at getResponse (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\api-framework\\lib\\pipeline.js:259:47)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Error parsing filter","time":"2025-05-25T08:58:03.450Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":29032,"level":50,"version":"5.115.1","err":{"id":"38dc7a80-3947-11f0-aaf1-2933b914b3ce","domain":"http://localhost:2368/","code":null,"name":"ThemeValidationError","statusCode":422,"level":"normal","message":"The currently active theme \"friedensrat\" has fatal errors.","stack":"ThemeValidationError: The currently active theme \"friedensrat\" has fatal errors.\n    at Object.getThemeValidationError (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\themes\\validate.js:144:12)\n    at module.exports.loadAndActivate (C:\\ghost_test\\versions\\5.115.1\\core\\server\\services\\themes\\activate.js:27:36)\n    at async initServicesForFrontend (C:\\ghost_test\\versions\\5.115.1\\core\\boot.js:186:5)\n    at async bootGhost (C:\\ghost_test\\versions\\5.115.1\\core\\boot.js:570:31)","hideStack":false,"errorDetails":"{\"checkedVersion\":\"5.x\",\"name\":\"friedensrat\",\"path\":\"C:\\\\ghost_test\\\\content\\\\themes\\\\friedensrat\",\"version\":\"1.0.0\",\"errors\":[{\"fatal\":true,\"level\":\"error\",\"rule\":\"Templates must contain valid Handlebars\",\"details\":\"Oops! You seemed to have used invalid Handlebars syntax. This mostly happens when you use a helper that is not supported.<br>See the full list of available helpers <a href=\\\"https://ghost.org/docs/themes/helpers/\\\" target=_blank>here</a>.\",\"failures\":[{\"ref\":\"tag.hbs\",\"message\":\"Missing helper: \\\"equals\\\"\"}],\"code\":\"GS005-TPL-ERR\"}],\"warnings\":[{\"fatal\":false,\"level\":\"warning\",\"rule\":\"Missing support for custom fonts\",\"details\":\"CSS variables for Ghost font settings are not present: <code>--gh-font-heading</code>, <code>--gh-font-body</code>\",\"regex\":{},\"failures\":[{\"ref\":\"styles\"}],\"code\":\"GS051-CUSTOM-FONTS\"}]}"},"msg":"The currently active theme \"friedensrat\" has fatal errors.","time":"2025-05-25T09:04:10.793Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":29032,"level":50,"version":"5.115.1","err":{"domain":"http://localhost:2368/","message":"#unless requires exactly one argument","stack":"Error: #unless requires exactly one argument\n    at Object.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\helpers\\if.js:35:13)\n    at Object.wrapper (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\internal\\wrapHelper.js:15:19)\n    at eval (eval at createFunctionContext (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\javascript-compiler.js:262:23), <anonymous>:10:54)\n    at prog (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:268:12)\n    at execIteration (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:88:27)\n    at C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:107:17\n    at arrayEach (C:\\ghost_test\\versions\\5.115.1\\node_modules\\lodash\\lodash.js:530:11)\n    at Function.forEach (C:\\ghost_test\\versions\\5.115.1\\node_modules\\lodash\\lodash.js:9410:14)\n    at iterateCollection (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:100:11)\n    at Object.foreach (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:115:9)\n    at Object.wrapper (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\internal\\wrapHelper.js:15:19)\n    at eval (eval at createFunctionContext (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\javascript-compiler.js:262:23), <anonymous>:10:134)\n    at Object.prog [as fn] (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:268:12)\n    at Object.get (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\get.js:315:34)\n    at async Object.returnAsync (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\services\\helpers\\handlebars.js:16:30)"},"msg":"#unless requires exactly one argument","time":"2025-05-25T09:05:31.913Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":29032,"level":50,"version":"5.115.1","err":{"domain":"http://localhost:2368/","message":"#unless requires exactly one argument","stack":"Error: #unless requires exactly one argument\n    at Object.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\helpers\\if.js:35:13)\n    at Object.wrapper (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\internal\\wrapHelper.js:15:19)\n    at eval (eval at createFunctionContext (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\javascript-compiler.js:262:23), <anonymous>:10:54)\n    at prog (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:268:12)\n    at execIteration (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:88:27)\n    at C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:107:17\n    at arrayEach (C:\\ghost_test\\versions\\5.115.1\\node_modules\\lodash\\lodash.js:530:11)\n    at Function.forEach (C:\\ghost_test\\versions\\5.115.1\\node_modules\\lodash\\lodash.js:9410:14)\n    at iterateCollection (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:100:11)\n    at Object.foreach (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:115:9)\n    at Object.wrapper (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\internal\\wrapHelper.js:15:19)\n    at eval (eval at createFunctionContext (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\javascript-compiler.js:262:23), <anonymous>:10:134)\n    at Object.prog [as fn] (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:268:12)\n    at Object.get (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\get.js:315:34)\n    at async Object.returnAsync (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\services\\helpers\\handlebars.js:16:30)"},"msg":"#unless requires exactly one argument","time":"2025-05-25T09:11:18.504Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":29032,"level":50,"version":"5.115.1","req":{"meta":{"requestId":"43c379bb-209b-4956-9117-f9ff29f591fa","userId":null},"url":"/articles/","method":"GET","originalUrl":"/articles/","params":{},"headers":{"host":"localhost:2368","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"de,en-US;q=0.7,en;q=0.3","accept-encoding":"gzip, deflate, br, zstd","connection":"keep-alive","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"none","sec-fetch-user":"?1","priority":"u=0, i"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"text/html; charset=utf-8","etag":"W/\"617-ds0neU40TvUrpmnJp4Nf09E5Kig\"","vary":"Accept-Encoding","content-encoding":"br"},"statusCode":500,"responseTime":"101ms"},"err":{"id":"37c98e70-3948-11f0-aaf1-2933b914b3ce","domain":"http://localhost:2368/","code":"UNEXPECTED_ERROR","name":"InternalServerError","statusCode":500,"level":"critical","message":"An unexpected error occurred, please try again.","context":"\"[default.hbs] options.fn is not a function\"","stack":"TypeError: [default.hbs] options.fn is not a function\n    at prepareError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\mw-error-handler\\lib\\mw-error-handler.js:113:19)\n    at Object.is (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\is.js:31:24)\n    at Object.wrapper (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\internal\\wrapHelper.js:15:19)\n    at Object.eval [as main] (eval at createFunctionContext (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\javascript-compiler.js:262:23), <anonymous>:48:235)\n    at main (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:208:32)\n    at ret (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:212:12)\n    at ret (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\compiler.js:519:21)\n    at renderTemplate (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:499:13)\n    at _stackRenderer (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:528:9)\n    at renderTemplate (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:508:5)\n    at render (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:535:5)\n    at renderIt (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:597:18)\n    at C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:607:11\n    at _returnLayouts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:131:7)\n    at C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:142:7\n    at FSReqCallback.readFileAfterClose [as oncomplete] (node:internal/fs/read/context:68:3)","hideStack":false},"msg":"An unexpected error occurred, please try again.","time":"2025-05-25T09:11:18.506Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":29032,"level":50,"version":"5.115.1","err":{"domain":"http://localhost:2368/","message":"#unless requires exactly one argument","stack":"Error: #unless requires exactly one argument\n    at Object.<anonymous> (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\helpers\\if.js:35:13)\n    at Object.wrapper (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\internal\\wrapHelper.js:15:19)\n    at eval (eval at createFunctionContext (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\javascript-compiler.js:262:23), <anonymous>:10:54)\n    at prog (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:268:12)\n    at execIteration (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:88:27)\n    at C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:107:17\n    at arrayEach (C:\\ghost_test\\versions\\5.115.1\\node_modules\\lodash\\lodash.js:530:11)\n    at Function.forEach (C:\\ghost_test\\versions\\5.115.1\\node_modules\\lodash\\lodash.js:9410:14)\n    at iterateCollection (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:100:11)\n    at Object.foreach (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\foreach.js:115:9)\n    at Object.wrapper (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\internal\\wrapHelper.js:15:19)\n    at eval (eval at createFunctionContext (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\javascript-compiler.js:262:23), <anonymous>:10:134)\n    at Object.prog [as fn] (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:268:12)\n    at Object.get (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\get.js:315:34)\n    at async Object.returnAsync (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\services\\helpers\\handlebars.js:16:30)"},"msg":"#unless requires exactly one argument","time":"2025-05-25T09:11:20.940Z","v":0}
{"name":"Log","hostname":"DESKTOP-7NUDL9S","pid":29032,"level":50,"version":"5.115.1","req":{"meta":{"requestId":"e5e48ca8-cc80-4e4c-be1e-061d50d8bbda","userId":null},"url":"/articles/","method":"GET","originalUrl":"/articles/","params":{},"headers":{"host":"localhost:2368","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-language":"de,en-US;q=0.7,en;q=0.3","accept-encoding":"gzip, deflate, br, zstd","connection":"keep-alive","upgrade-insecure-requests":"1","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"none","sec-fetch-user":"?1","priority":"u=0, i"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"text/html; charset=utf-8","etag":"W/\"617-ds0neU40TvUrpmnJp4Nf09E5Kig\"","vary":"Accept-Encoding","content-encoding":"br"},"statusCode":500,"responseTime":"91ms"},"err":{"id":"393d69c0-3948-11f0-aaf1-2933b914b3ce","domain":"http://localhost:2368/","code":"UNEXPECTED_ERROR","name":"InternalServerError","statusCode":500,"level":"critical","message":"An unexpected error occurred, please try again.","context":"\"[default.hbs] options.fn is not a function\"","stack":"TypeError: [default.hbs] options.fn is not a function\n    at prepareError (C:\\ghost_test\\versions\\5.115.1\\node_modules\\@tryghost\\mw-error-handler\\lib\\mw-error-handler.js:113:19)\n    at Object.is (C:\\ghost_test\\versions\\5.115.1\\core\\frontend\\helpers\\is.js:31:24)\n    at Object.wrapper (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\internal\\wrapHelper.js:15:19)\n    at Object.eval [as main] (eval at createFunctionContext (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\javascript-compiler.js:262:23), <anonymous>:48:235)\n    at main (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:208:32)\n    at ret (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\runtime.js:212:12)\n    at ret (C:\\ghost_test\\versions\\5.115.1\\node_modules\\handlebars\\dist\\cjs\\handlebars\\compiler\\compiler.js:519:21)\n    at renderTemplate (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:499:13)\n    at _stackRenderer (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:528:9)\n    at renderTemplate (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:508:5)\n    at render (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:535:5)\n    at renderIt (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:597:18)\n    at C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:607:11\n    at _returnLayouts (C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:131:7)\n    at C:\\ghost_test\\versions\\5.115.1\\node_modules\\express-hbs\\lib\\hbs.js:142:7\n    at FSReqCallback.readFileAfterClose [as oncomplete] (node:internal/fs/read/context:68:3)","hideStack":false},"msg":"An unexpected error occurred, please try again.","time":"2025-05-25T09:11:20.941Z","v":0}

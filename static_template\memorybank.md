# Ghost CMS Template Implementation Project

This document serves as a reference for the steps taken to implement a Ghost CMS compatible HTML template based on the Figma design.

## Table of Contents

1. [Project Structure Setup](#project-structure-setup)
2. [Header Implementation](#header-implementation)
3. [Article Slider Implementation](#article-slider-implementation)
4. [Text Antialiasing Improvements](#text-antialiasing-improvements)
5. [Article Image Height Variation](#article-image-height-variation)
6. [Article Image Implementation](#article-image-implementation)
7. [UI Refinements](#ui-refinements)
8. [Full-Width Layout Implementation](#full-width-layout-implementation)
9. [Layout Refinements and Content Expansion](#layout-refinements-and-content-expansion)
10. [Responsive Adjustments (Mobile & Tablet)](#responsive-adjustments-mobile-tablet)
11. [Mobile Navigation Implementation (Scaling Hamburger)](#mobile-navigation-implementation-scaling-hamburger)
12. [Navigation Dropdowns Implementation](#navigation-dropdowns-implementation)
13. [Final Adjustments & Refinements](#final-adjustments-refinements)
14. [Upcoming Features](#upcoming-features)
15. [Fluid Typography Implementation](#fluid-typography-implementation)
16. [Newsletter Section Implementation](#newsletter-section-implementation)
17. [CSS Reorganization for Better Maintainability](#css-reorganization-for-better-maintainability)
18. [Mobile Navigation Dropdown Implementation](#mobile-navigation-dropdown-implementation)
19. [All Articles Page Implementation](#all-articles-page-implementation)
20. [Fluid Article Grid Implementation](#fluid-article-grid-implementation)

## Project Structure Setup

- Created basic directory structure:
  - `/css/` - For stylesheets
  - `/js/` - For JavaScript files
  - `/img/` - For images and other media
  - `/fonts/` - For font files

- Set up initial files:
  - `index.html` - Main HTML file
  - `css/normalize.css` - CSS reset
  - `css/fonts.css` - Font declarations
  - `css/style.css` - Main stylesheet
  - `js/main.js` - Main JavaScript file

## Header Implementation

### Initial Implementation

- Created responsive header section with Figma-specified dimensions and styling
- Implemented the header logo using SVG
- Added navigation menu items
- Positioned header content box at the bottom left

### Header Improvements

- Updated header height from 70vh to 85vh for better visual balance
- Replaced pixel measurements with relative units (rem, vh) for better responsiveness
- Added fluid typography with clamp() for responsive text sizing
- Added hover effects to menu items and buttons
- Enhanced mobile responsiveness with appropriate breakpoints

### Header Image & Logo

- Added header background image (header-background.jpg)
- Implemented SVG logo with proper sizing
- Applied filter to make SVG logo white on dark background
- Fixed accessibility concerns by ensuring proper alt text

## Article Slider Implementation

### Article Slider Structure

- Created horizontally scrollable article grid based on Figma design
- Implemented 5 article cards with placeholder images
- Added navigation controls (previous/next buttons)
- Structured article cards with:
  - Image
  - Date
  - Title
  - Category tag

### Article Slider Functionality

- Added JavaScript for horizontal scrolling functionality
- Implemented previous/next button logic
- Added proper button state management (disabled when at start/end)
- Optimized for touch devices with swipe gestures

### Ghost CMS Compatibility

- Added data attributes for easy conversion to Ghost handlebars templates
- Included a commented section with Ghost template code for future implementation
- Structured HTML semantically for better SEO and accessibility
- Prepared article categories to work with Ghost CMS tags system

## Text Antialiasing Improvements

### Font Loading Optimization

- Added proper font declarations with font-display: swap
- Implemented font-feature-settings for better typography
- Added font preloading for critical fonts
- Specified proper unicode ranges for Albert Sans font

### Text Rendering Enhancements

- Added -webkit-font-smoothing: antialiased for smooth text on WebKit browsers
- Added -moz-osx-font-smoothing: grayscale for Firefox
- Applied text-rendering: optimizeLegibility for better legibility
- Added appropriate letter-spacing to match Figma design
- Applied subtle text shadows for improved text appearance
- Added font-synthesis: none to prevent faux bold/italic

### Typography Adjustments

- Updated specific text elements to match Figma's rendering
- Added proper letter-spacing for headings and article elements
- Applied appropriate font-feature-settings for different text types
- Enhanced article category tags with better padding (0.25rem 0.7rem)

## Article Image Height Variation

### Visual Enhancement

- Implemented alternating image heights in the article slider to match the Figma design
- Created a repeating pattern of varying heights (standard, taller, tallest, taller)
- Used CSS nth-child selectors to ensure pattern works with dynamic content

### Ghost CMS Compatibility

- Designed the alternating height system to work with Ghost's templating system
- Used CSS-only approach (no JavaScript or custom attributes required)
- Ensured the pattern would dynamically adapt to newly added articles in Ghost

### Responsive Considerations

- Adjusted the height variation pattern across different breakpoints
- Used both fixed (rem) units for larger screens and viewport (vw) units for smallest screens
- Maintained visual interest across all device sizes while keeping content readable

### Enhanced Height Variation

- Increased height differences between articles for more dramatic visual impact
- Created a significantly taller article (25rem/400px height) for the third article
- Adjusted all height relationships for better rhythm and flow
- Updated responsive values to maintain proportional differences on smaller screens

## Article Image Implementation

### Integration of Real Images

- Replaced placeholder images with actual article images
- Added new folder structure (img/article-images/) for article media
- Assigned appropriate images to each article in the slider
- Enhanced image styling to ensure proper display with object-fit: cover

### Responsiveness Improvements

- Added display: block to prevent unwanted image gaps
- Maintained aspect ratio control with object-fit
- Ensured images properly fill their containers at varying heights
- Preserved hover effects with the real images

### Ghost CMS Preparation

- Kept data-ghost-img attributes for future Ghost integration
- Made sure image structure is compatible with Ghost's image handling
- Ensured the responsive design will work with dynamic Ghost content

## UI Refinements

### Hover Effect Improvements

- Modified hover behavior to only apply zoom effect to the image, not the entire article
- Removed the translateY effect on article hover for more focused interaction
- Applied transform: scale(1.05) only to the image itself when container is hovered
- Enhanced transition with smoother animation on the image container

### Visual Hierarchy Adjustments

- Strengthened visual hierarchy with more dramatic height variations
- Created a focal point with the tallest image at 25rem (400px)
- Improved the rhythm of the overall slider with balanced height relationships
- Maintained article content alignment while varying image heights

## Full-Width Layout Implementation

### Container Structure Changes

- Removed max-width constraint from the main container
- Implemented consistent container padding of 0.5rem on both sides
- Added 0.5rem border radius to the header for a slightly rounded appearance
- Maintained full-width appearance while allowing for subtle visual refinements

### Responsive Container Padding

- Set consistent 0.5rem padding across all breakpoints
- Simplified the responsive design with uniform padding
- Removed extra padding adjustments for cleaner alignment
- Ensured consistent spacing in the article grid section

### Slider Refinements

- Changed slider heading alignment from center to left
- Adjusted slider grid to align items to the left (flex-start)
- Maintained the horizontal scrolling functionality within the full-width layout
- Created a more balanced visual connection between the header and slider sections

### Ghost CMS Compatibility

- Structured the full-width layout to be compatible with Ghost's content model
- Used CSS variables for layout values that can be easily customized in theme settings
- Ensured the responsive container approach will work with Ghost's template system

## Layout Refinements and Content Expansion

### Inner Padding Implementation

- Added larger inner padding (2.813rem) for content elements while keeping minimal outer container padding (0.5rem)
- Created distinct separation between outer page boundaries and inner content
- Used CSS variables (--inner-padding) for consistent content alignment across sections
- Adjusted the header, slider heading, and content to align with the new inner padding
- Implemented responsive inner padding that scales with screen size

### Slider Content Expansion

- Added 4 additional article placeholders (9 total) to better fill wide screens
- Created more realistic representation of a content-rich article slider
- Included varied content types across the additional articles
- Maintained alternating image height pattern across all articles

### Overflow Handling

- Enabled proper overflow for the article slider on the right side
- Added margin correction to prevent abrupt cutoff of slider content
- Ensured the slider can be smoothly scrolled beyond the visible area
- Aligned slider controls with the inner padding for consistent spacing

### Enhanced Visual Hierarchy

- Created clear alignment between the header content and slider content
- Used consistent inner padding to establish visual rhythm across sections
- Improved the desktop experience for wider screens with more content
- Maintained responsiveness while providing richer content density

## Responsive Adjustments (Mobile & Tablet)

- **Team Section:**
    - On tablet breakpoints (`<= 768px`), the `.team-select` area now stacks vertically (`flex-direction: column`), placing the team member info below the portraits for better readability.
- **Highlights & Events Sections:**
    - On mobile breakpoints (`<= 768px`), reduced the left/right padding for content within these sections (`.events-header`, `.events-grid`, `.card-content`) to `0.5rem` for better use of screen space.
- **Events Section:**
    - On tablet/mobile breakpoints (`<= 768px`), the minimum height (`min-height`) of individual event items (`.event-item`) was reduced to `20rem`.
- **Highlights Section:**
    - On small mobile breakpoints (`<= 576px`), the padding for highlight card content (`.card-content`) was adjusted to `2.375rem` as specified.

## Mobile Navigation Implementation (Scaling Hamburger)

- **Goal:** Implement a hamburger-style mobile navigation for smaller screens (<= 992px), using a new pre-made component.
- **Component Source:** Based on the updated `mobile-nav.html`, `mobile-nav.css`, `mobile-nav.js` files provided by the user.
- **HTML Structure (`index.html`):**
    - Removed the previous mobile navigation attempt.
    - Added the new component's `<nav class="navigation">` structure directly after `<body>`. This component includes its own toggle button (`.hamburger-nav__toggle`) within the `.hamburger-nav` element.
    - Populated the `.hamburger-nav__ul` with the site's navigation links, using the `.hamburger-nav__li > a > p + div.hamburger-nav__dot` structure.
- **CSS Styling (`style.css` & `mobile-nav.css`):**
    - In `style.css`, added media queries (`@media (max-width: 992px)`) to hide the desktop `.menu` and show the entire mobile `<nav class="navigation">` component.
    - Used the user-provided `mobile-nav.css`, adapting it further:
        - Adjusted fonts (`Atkinson Hyperlegible Next`), colors (using CSS variables `--white`, `--text-dark`, `--dark-blue`), padding, and sizing (`.hamburger-nav`, `.hamburger-nav__group`, link styles) to align with the main site's design.
        - Ensured the top-right positioning and scaling animation were retained but visually integrated.
- **JavaScript (`mobile-nav.js`):**
    - Used the user-provided `mobile-nav.js` without modification, as it uses attribute selectors (`[data-navigation-status]`, `[data-navigation-toggle]`) compatible with the new HTML.
- **File Linking:** Ensured `mobile-nav.css` and `mobile-nav.js` are linked in `index.html`.

## Navigation Dropdowns Implementation

- **Goal:** Add dropdown functionality to both desktop and mobile navigation menus, mirroring the old site structure.
- **HTML Structure (`index.html`):**
    - Updated both desktop (`ul.menu`) and mobile (`.hamburger-nav__ul`) structures.
    - Added nested `ul.dropdown-menu` inside the `li.has-dropdown` for the 'Über uns' item in both desktop (`ul.menu`) and mobile (`.hamburger-nav__ul`) navigations.
    - Added example sub-menu items ('Team', 'Geschichte').
    - Added `aria-haspopup="true"` and `aria-expanded="false"` to parent links.
    - Replaced `span` indicators with `img.dropdown-indicator-svg` (desktop) and `img.dropdown-toggle-icon-svg` (mobile) using `caret-down.svg`.
- **Desktop CSS (`style.css`):**
    - Styled `li.has-dropdown` as `position: relative`.
    - Styled `ul.dropdown-menu` for absolute positioning below the parent, with background, shadow, and initial hidden state (using `visibility`, `opacity`, `transform`).
    - Implemented hover effect (`li.has-dropdown:hover > ul.dropdown-menu`) to show the dropdown.
    - Styled dropdown links and SVG indicator (size, rotation on hover).
    - Used `filter: brightness(0) invert(1);` to make the indicator SVG white.
- **Mobile CSS (`mobile-nav.css`):**
    - Styled nested `ul.dropdown-menu` for indentation and initial hidden state (`max-height: 0`).
    - Removed CSS arrow styles and styled the `img.dropdown-toggle-icon-svg` (size, rotation when `.is-open`).
    - Used `filter: brightness(0) invert(1);` to make the toggle icon SVG white.
    - Added styles for `.has-dropdown.is-open > ul.dropdown-menu` to reveal the submenu via `max-height`.
- **Mobile JavaScript (`main.js`):**
    - No changes needed; existing `initMobileNavDropdowns` function handles toggling for any `.has-dropdown` item.
- **Ghost CMS Considerations:** Structure uses standard nested lists, compatible with Ghost's `{{navigation}}` helper. The theme needs logic (e.g., in `navigation.hbs`) to dynamically add `.has-dropdown` class and ARIA attributes to items with children (`{{#if children}}...{{/if}}`). Deeper nesting (level 3+) might require additional theme CSS/JS adjustments.

## Final Adjustments & Refinements

- **Mobile Nav:** Increased base font size for navigation links (`.hamburger-nav__p`) to `1.2rem` for better readability on smaller screens.
- **Highlights Section:** Adjusted responsiveness so that the text content (`.top`, `.highlight-description`) reverts to `max-width: 100%` on screens 1280px wide or less.
- **Highlights Section:** Adjusted responsiveness so that the padding for highlight card content (`.card-content`) changes to `2.375rem` on screens 1280px wide or less.
- **Article Slider:** Reduced the height differences between alternating article images (`.article-image-container`) within tablet and mobile media queries (`992px`, `768px`, `576px`) for a less extreme visual effect on smaller screens. Added `max-height` in `rem` units for the `576px` breakpoint to prevent excessive height on very narrow/tall screens.
- **Header Logo:** Changed the `.logo-svg` width from fixed pixel values at breakpoints to a fluid `clamp(10rem, 10vw + 8rem, 18.75rem)` value for smoother scaling. Removed breakpoint-specific width overrides.
- **Article Slider Divider:** Added a subtle `border-bottom` (1px solid, low opacity) to the `.article-slider` section to visually separate it from the subsequent highlights section. Added `padding-bottom` to the section to space the content correctly above the new border.
- **Highlights Section Mobile Order:** Modified the CSS in the `@media (max-width: 768px)` query to specifically set `grid-template-areas: "content" "image";` for the second highlight card (`.cards-row.mobile-flip`), reversing its default mobile order (image above content) so that the content appears first on smaller screens.
- **Highlights Section Layout:** Adjusted CSS for desktop (`> 768px`): First card (`.cards-row:not(.mobile-flip)`) is content/image, second card (`.cards-row.mobile-flip`) is image/content. On smaller screens (`<= 768px`), both cards stack vertically with image above content.
- **Team Section Quote Height:** Changed `.team-wrapper` to use `display: grid` with `grid-template-rows: auto auto;` and a fluid `row-gap: clamp(5rem, 15vw, 19rem);`. This creates a fluid vertical gap *between* the `.team-header` and the `.quote-content` within the top content area, aiming for height stability similar to the `team-example.html` reference, without relying on fixed heights.
- **Team Section Typography:** Converted fixed font sizes for `.team-heading`, `.member-name`, `.member-title`, `.quote-text`, and `.team-cta-button` to use fluid `clamp()` values. Removed breakpoint-specific font size overrides for quote text and CTA button.
- **Fluid Typography Refinements:**
    - Adjusted `clamp()` for `.quote-text` to achieve a smaller minimum size (~1.25rem) and potentially earlier scaling.
    - Converted fixed font sizes for event items (`.event-date`, `.event-tag`, `.event-description p`) to use fluid `clamp()` values.
    - Adjusted `clamp()` for `.event-title` for improved fluid scaling.
- **Fluid Typography Tuning:**
    - Further adjusted the preferred value (middle part with `vw`) in `clamp()` functions for several elements (e.g., header H1/P, quote text, event titles/metadata) to increase sensitivity to viewport width changes, making the fluid scaling effect start earlier on larger screens.

### Typography Refinements

- Updated navigation menu to use Atkinson Hyperlegible Next font family instead of Albert Sans
- Standardized font usage across the site for better typography consistency
- Applied proper font weights to maintain visual hierarchy
- Ensured consistent typographic style matching the Figma design

### Call-to-Action Improvements

- Replaced the arrow graphics in the header CTA button with the new cta-arrow.svg
- Reduced the font-weight of the CTA button text from 600 to 500 for better visual balance
- Adjusted the arrow icon size for better proportion with the button text
- Maintained the hover animation effects for interactive feedback

## Upcoming Features

- Feature section
- About section
- Footer
- Full Ghost CMS template conversion
- Accessibility improvements
- Performance optimizations

## Footer Implementation

### Figma Analysis and HTML Structure
- Analyzed the footer design from the provided Figma link (node `1-290`).
- Extracted layout information using Figma MCP tool, noting the top section (`footer-content-top`) uses a column layout.
- Created semantic HTML structure in `index.html`:
    - `footer-content-top`: Contains two main children vertically stacked.
        - Child 1 (`.footer-top-row`): A row containing `footer-company-data` (left) and `footer-nav` (right).
            - Used `logo_footer.svg` for the company logo.
        - Child 2 (`.footer-donate`): A full-width row placed below the first child.
    - `footer-content-bottom`: Contains copyright and legal links.
- Added the footer section directly after the `<section class="team-section">`.

### Ghost CMS Compatibility
- Included comments within the HTML indicating potential Ghost CMS template variables and helpers (e.g., `{{@site.title}}`, `{{navigation}}`, `{{date format="YYYY"}}`).
- Structured the footer sections (company info, navigation, legal links) to facilitate easy replacement with dynamic Ghost data.

### CSS Styling and Responsiveness
- Added CSS rules to `css/style.css` to style the footer according to the Figma design (`.footer`, `.footer-wrapper`, etc.).
- Matched background color (`#C3C3FF`), typography (using Atkinson Hyperlegible Next), and spacing.
- Styled `footer-content-top` as a flex column.
- Styled the inner `.footer-top-row` as a flex row with `space-between` alignment for company data and navigation.
- Positioned the `.footer-donate` section below the top row.
    - Replaced the text heading with an `img` tag referencing `unterstutzen.svg` (`.donate-svg-graphic`).
    - Styled the SVG to fill the container width (`width: 100%; height: auto;`).
    - Made the `.footer-donate` container `position: relative;`.
    - Centered the `.footer-donate-button` absolutely on top of the SVG (`position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);`).
- Used existing CSS variables like `--inner-padding` for consistent layout.
- Implemented responsive styles using media queries to ensure the footer adapts correctly (stacking elements, adjusting flex layouts, modifying donate section layout and button size on smaller screens).
- Added a subtle border separator between the top and bottom footer content.

## Reference Materials

- [Figma Design](https://www.figma.com/design/EGtQqBmYEGdR8EOqcvsmN0/Friedensrat-Webseite?node-id=1-1528&t=MuuqT6nkF80cXtYM-4) - Original design reference
- [Northzone Website](https://northzone.com/) - Inspiration for article slider and full-width layout
- [Ghost CMS Documentation](https://ghost.org/docs/themes/) - For theme development guidelines

## Fluid Typography Implementation

- **Goal:** Implement a more fluid and responsive typography system that scales text smoothly based on viewport width, similar to Finsweet's approach.
- **Implementation Steps:**
    1. Created a dedicated `css/fluid-typography.css` file for better maintainability of typography-related styles.
    2. Extracted all font-size related declarations and variables from `style.css` into this new file.
    3. Organized typography variables by section (Header, Article, Highlights, Events, Team, Footer).
    4. Implemented root-font scaling using mathematically precise calculations:
       ```css
       /* For large screens (1280px to 1920px) */
       @media screen and (min-width: 1280px) and (max-width: 1920px) {
         html {
           font-size: calc(12px + 0.3125vw); /* 16px at 1280px, 18px at 1920px */
         }
       }
       
       /* For medium screens (768px to 1280px) */
       @media screen and (min-width: 768px) and (max-width: 1279px) {
         html {
           font-size: calc(13.5px + 0.1953125vw); /* 15px at 768px, 16px at 1280px */
         }
       }
       
       /* For small screens (320px to 767px) */
       @media screen and (max-width: 767px) {
         html {
           font-size: calc(13.284px + 0.22371vw); /* 14px at 320px, 15px at 767px */
         }
       }
       ```
    5. Enhanced the responsiveness of individual text elements using `clamp()` with higher `vw` multipliers in the preferred values.
- **Benefits:**
    - Text scales immediately and smoothly as the viewport changes.
    - Typography is easier to maintain with all related styles in one file.
    - Mathematical precision in calculations ensures fluid text at all screen widths.
    - Base font sizes scale independently of individual element scaling for more fine-tuned control.
    - The system respects minimum and maximum size constraints while providing fluid scaling in between.

## Newsletter Section Implementation

- **Goal:** Add a newsletter subscription section above the footer to capture user emails, following the Figma design.
- **Implementation Steps:**
    1. Analyzed the Figma design (`node-id=1-274`) to extract layout, colors, and typography details.
    2. Added new HTML structure in `index.html` right before the footer section:
       - Created `.newsletter-section` with a wrapper that contains a heading and form
       - Implemented an email input field, submit button and privacy notice text with link
       - Added Ghost CMS conversion comments for future integration
    3. Added CSS styles in `style.css` that match the Figma design:
       - Dark blue background (`var(--dark-blue)`) with proper padding and border radius
       - Input field and button styling with hover effects
       - Responsive layout that shifts from horizontal to vertical on smaller screens
    4. Updated `fluid-typography.css` to include newsletter-specific variables:
       - Added variables for the heading, input text, button, and privacy text
       - Used `clamp()` functions to ensure fluid scaling across different viewport sizes
       - Applied the variables to the corresponding elements
- **Ghost CMS Integration Preparation:**
    - Added `data-members-form="subscribe"` attribute for Ghost membership functionality
    - Included success and error message containers for form feedback
    - Added Ghost handlebars syntax in comments for easy future conversion
- **Responsive Considerations:**
    - On smaller screens (< 992px), the layout changes from side-by-side to stacked
    - On very small screens (< 576px), the privacy notice text stacks vertically
    - Maintained consistent styling with the rest of the site through CSS variables

## CSS Reorganization for Better Maintainability

- **Goal:** Improve code organization and maintainability by splitting CSS files according to their purpose.
- **Implementation Steps:**
    1. Analyzed the existing CSS structure to identify logical separation points
    2. Created a new file organization:
       - `main.css` - Contains base site-wide styles and variables
       - `style.css` - Holds component-specific styles for each section
       - `responsive.css` - Dedicated to all media queries and responsive adjustments
       - `fluid-typography.css` - Focuses on typography scaling system (unchanged)
       - `mobile-nav.css` - Mobile navigation styles (unchanged)
    3. Updated HTML to include the new CSS files in the proper order:
       ```html
       <link rel="stylesheet" href="css/normalize.css">
       <link rel="stylesheet" href="css/fonts.css">
       <link rel="stylesheet" href="css/style.css">
       <link rel="stylesheet" href="css/fluid-typography.css">
       <link rel="stylesheet" href="css/main.css">
       <link rel="stylesheet" href="css/responsive.css">
       <link rel="stylesheet" href="css/mobile-nav.css">
       ```
- **Benefits:**
    - Improved separation of concerns
    - Easier to find and modify specific styles
    - Reduced file sizes make each file more manageable
    - Clear organization based on style function rather than mixing everything together
    - Responsive styles are isolated making them easier to adjust without affecting base styles 

## Mobile Navigation Dropdown Implementation

- **Goal:** Add dropdown functionality to the mobile navigation to match the desktop navigation and prepare for Ghost CMS integration.
- **Implementation Steps:**
    1. Enhanced the mobile-nav.js to add dropdown toggle functionality:
       - Added event listeners to dropdown toggle elements
       - Implemented toggle for "is-open" class on parent li elements
       - Added ARIA attribute updates for accessibility
    2. Added Ghost CMS templating comments to both navigations:
       - Created template code using Ghost's navigation helpers with #foreach loops
       - Structured templates to maintain the same HTML structure and class names
       - Ensured both mobile and desktop navigation can use the same Ghost navigation data source
    3. Both navigations now maintain consistent structure while being ready for Ghost CMS conversion
    4. Dropdowns are fully functional on mobile with proper accessibility attributes 

## All Articles Page Implementation

- **Goal:** Create a dedicated "All Articles" page based on the Figma design that displays a collection of articles with filtering capabilities.
- **Implementation Steps:**
    1. Created a new HTML file (`articles.html`) based on the existing `index.html` structure:
       - Reused navigation and footer components for consistency 
       - Maintained the same layout structure and CSS organization
    2. Implemented a distinctive header section with an article highlight area:
       - Added orange background (#D84406) to match the design
       - Created a featured article showcase with title, excerpt, and image
       - Included meta information (author, category, date)
    3. Developed an article filtering system:
       - Created horizontally scrollable category filter tags (All, Artikel, Internes, etc.)
       - Implemented active state styling for selected filters
       - Added JavaScript functionality to highlight category-matching articles
    4. Implemented the article grid layout:
       - Created a responsive 3-column grid (3 columns on desktop, 2 on tablet, 1 on mobile)
       - Styled article cards with consistent spacing and typography
       - Added hover effects on article images (scale: 1.05)
       - Included category-specific color coding for visual distinction
    5. Added pagination controls:
       - Created previous/next buttons with appropriate disabled states
       - Implemented numbered page links with active state styling
       - Added JavaScript for pagination navigation
    6. Created supporting files:
       - `articles.css` for page-specific styling and responsive behavior
       - `articles.js` for interactive functionality (filtering and pagination)
       - Added Ghost CMS conversion notes as HTML comments for future implementation
    7. Maintained design consistency with the main site:
       - Used the same typography system, spacing, and color variables
       - Ensured responsive behavior matched the main site's approach
       - Applied the established design language across all elements

## Upcoming Features

- Feature section
- About section
- Footer
- Full Ghost CMS template conversion
- Accessibility improvements
- Performance optimizations

## Footer Implementation

### Figma Analysis and HTML Structure
- Analyzed the footer design from the provided Figma link (node `1-290`).
- Extracted layout information using Figma MCP tool, noting the top section (`footer-content-top`) uses a column layout.
- Created semantic HTML structure in `index.html`:
    - `footer-content-top`: Contains two main children vertically stacked.
        - Child 1 (`.footer-top-row`): A row containing `footer-company-data` (left) and `footer-nav` (right).
            - Used `logo_footer.svg` for the company logo.
        - Child 2 (`.footer-donate`): A full-width row placed below the first child.
    - `footer-content-bottom`: Contains copyright and legal links.
- Added the footer section directly after the `<section class="team-section">`.

### Ghost CMS Compatibility
- Included comments within the HTML indicating potential Ghost CMS template variables and helpers (e.g., `{{@site.title}}`, `{{navigation}}`, `{{date format="YYYY"}}`).
- Structured the footer sections (company info, navigation, legal links) to facilitate easy replacement with dynamic Ghost data.

### CSS Styling and Responsiveness
- Added CSS rules to `css/style.css` to style the footer according to the Figma design (`.footer`, `.footer-wrapper`, etc.).
- Matched background color (`#C3C3FF`), typography (using Atkinson Hyperlegible Next), and spacing.
- Styled `footer-content-top` as a flex column.
- Styled the inner `.footer-top-row` as a flex row with `space-between` alignment for company data and navigation.
- Positioned the `.footer-donate` section below the top row.
    - Replaced the text heading with an `img` tag referencing `unterstutzen.svg` (`.donate-svg-graphic`).
    - Styled the SVG to fill the container width (`width: 100%; height: auto;`).
    - Made the `.footer-donate` container `position: relative;`.
    - Centered the `.footer-donate-button` absolutely on top of the SVG (`position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);`).
- Used existing CSS variables like `--inner-padding` for consistent layout.
- Implemented responsive styles using media queries to ensure the footer adapts correctly (stacking elements, adjusting flex layouts, modifying donate section layout and button size on smaller screens).
- Added a subtle border separator between the top and bottom footer content.

## Reference Materials

- [Figma Design](https://www.figma.com/design/EGtQqBmYEGdR8EOqcvsmN0/Friedensrat-Webseite?node-id=1-1528&t=MuuqT6nkF80cXtYM-4) - Original design reference
- [Northzone Website](https://northzone.com/) - Inspiration for article slider and full-width layout
- [Ghost CMS Documentation](https://ghost.org/docs/themes/) - For theme development guidelines

## Fluid Typography Implementation

- **Goal:** Implement a more fluid and responsive typography system that scales text smoothly based on viewport width, similar to Finsweet's approach.
- **Implementation Steps:**
    1. Created a dedicated `css/fluid-typography.css` file for better maintainability of typography-related styles.
    2. Extracted all font-size related declarations and variables from `style.css` into this new file.
    3. Organized typography variables by section (Header, Article, Highlights, Events, Team, Footer).
    4. Implemented root-font scaling using mathematically precise calculations:
       ```css
       /* For large screens (1280px to 1920px) */
       @media screen and (min-width: 1280px) and (max-width: 1920px) {
         html {
           font-size: calc(12px + 0.3125vw); /* 16px at 1280px, 18px at 1920px */
         }
       }
       
       /* For medium screens (768px to 1280px) */
       @media screen and (min-width: 768px) and (max-width: 1279px) {
         html {
           font-size: calc(13.5px + 0.1953125vw); /* 15px at 768px, 16px at 1280px */
         }
       }
       
       /* For small screens (320px to 767px) */
       @media screen and (max-width: 767px) {
         html {
           font-size: calc(13.284px + 0.22371vw); /* 14px at 320px, 15px at 767px */
         }
       }
       ```
    5. Enhanced the responsiveness of individual text elements using `clamp()` with higher `vw` multipliers in the preferred values.
- **Benefits:**
    - Text scales immediately and smoothly as the viewport changes.
    - Typography is easier to maintain with all related styles in one file.
    - Mathematical precision in calculations ensures fluid text at all screen widths.
    - Base font sizes scale independently of individual element scaling for more fine-tuned control.
    - The system respects minimum and maximum size constraints while providing fluid scaling in between.

## Newsletter Section Implementation

- **Goal:** Add a newsletter subscription section above the footer to capture user emails, following the Figma design.
- **Implementation Steps:**
    1. Analyzed the Figma design (`node-id=1-274`) to extract layout, colors, and typography details.
    2. Added new HTML structure in `index.html` right before the footer section:
       - Created `.newsletter-section` with a wrapper that contains a heading and form
       - Implemented an email input field, submit button and privacy notice text with link
       - Added Ghost CMS conversion comments for future integration
    3. Added CSS styles in `style.css` that match the Figma design:
       - Dark blue background (`var(--dark-blue)`) with proper padding and border radius
       - Input field and button styling with hover effects
       - Responsive layout that shifts from horizontal to vertical on smaller screens
    4. Updated `fluid-typography.css` to include newsletter-specific variables:
       - Added variables for the heading, input text, button, and privacy text
       - Used `clamp()` functions to ensure fluid scaling across different viewport sizes
       - Applied the variables to the corresponding elements
- **Ghost CMS Integration Preparation:**
    - Added `data-members-form="subscribe"` attribute for Ghost membership functionality
    - Included success and error message containers for form feedback
    - Added Ghost handlebars syntax in comments for easy future conversion
- **Responsive Considerations:**
    - On smaller screens (< 992px), the layout changes from side-by-side to stacked
    - On very small screens (< 576px), the privacy notice text stacks vertically
    - Maintained consistent styling with the rest of the site through CSS variables

## CSS Reorganization for Better Maintainability

- **Goal:** Improve code organization and maintainability by splitting CSS files according to their purpose.
- **Implementation Steps:**
    1. Analyzed the existing CSS structure to identify logical separation points
    2. Created a new file organization:
       - `main.css` - Contains base site-wide styles and variables
       - `style.css` - Holds component-specific styles for each section
       - `responsive.css` - Dedicated to all media queries and responsive adjustments
       - `fluid-typography.css` - Focuses on typography scaling system (unchanged)
       - `mobile-nav.css` - Mobile navigation styles (unchanged)
    3. Updated HTML to include the new CSS files in the proper order:
       ```html
       <link rel="stylesheet" href="css/normalize.css">
       <link rel="stylesheet" href="css/fonts.css">
       <link rel="stylesheet" href="css/style.css">
       <link rel="stylesheet" href="css/fluid-typography.css">
       <link rel="stylesheet" href="css/main.css">
       <link rel="stylesheet" href="css/responsive.css">
       <link rel="stylesheet" href="css/mobile-nav.css">
       ```
- **Benefits:**
    - Improved separation of concerns
    - Easier to find and modify specific styles
    - Reduced file sizes make each file more manageable
    - Clear organization based on style function rather than mixing everything together
    - Responsive styles are isolated making them easier to adjust without affecting base styles 

## Fluid Article Grid Implementation

### Analysis of Trawelt Blog Layout

We analyzed the Trawelt blog (https://www.trawelt.com/blog/) to understand how they implemented their fluid article grid:

- At 1920px viewport width, they use a grid with 4 columns (each approx. 438px wide)
- Their grid has a column gap of around 30px and row gap of 125px
- The grid container spans nearly the full viewport width (1843px)
- They use CSS `clamp()` functions for fluid spacing and typography
- The layout isn't constrained to a fixed max-width, allowing content to scale fluidly with the viewport

### CSS Variables for Fluid Scaling

We implemented a system of CSS variables for fluid scaling:

```css
:root {
    /* Spacing variables using clamp() for fluid scaling */
    --article-gap-row: clamp(2rem, 3vw, 5rem);     /* Row gap between articles */
    --article-gap-col: clamp(1.5rem, 2vw, 3rem);   /* Column gap between articles */
    --article-padding: clamp(1.5rem, 5vw, 3rem);   /* Horizontal padding for article grid */
    
    /* Typography scaling */
    --article-title-size: clamp(1.4rem, 1.2vw, 1.8rem);     /* Article card title */
    --article-excerpt-size: clamp(1rem, 0.8vw, 1.2rem);     /* Article card excerpt */
    --article-meta-size: clamp(0.8rem, 0.6vw, 1rem);        /* Article meta text */
    
    /* Card element spacing */
    --article-card-gap: clamp(0.8rem, 0.8vw, 1.5rem);       /* Gap between card elements */
    --article-meta-gap: clamp(0.5rem, 0.5vw, 0.8rem);       /* Gap between meta items */
    --article-content-padding: clamp(0rem, 1vw, 1.5rem);    /* Right padding for content */
}
```

These variables are modified at different breakpoints to maintain optimal spacing and proportions across all devices.

### Responsive Grid Implementation

We implemented a grid layout that adapts to different screen sizes:

```css
.article-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--article-gap-row) var(--article-gap-col);
    width: 100%;
    max-width: 100%; /* Allow full width expansion */
}

@media (min-width: 1440px) {
    .article-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 1439px) and (min-width: 992px) {
    .article-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 991px) and (min-width: 768px) {
    .article-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 767px) {
    .article-grid {
        grid-template-columns: 1fr;
        max-width: 500px; /* Limit width for better readability on mobile */
        margin-left: auto;
        margin-right: auto;
    }
}
```

### Aspect Ratio-Based Images

Instead of using fixed heights for article images, we implemented an aspect ratio approach that ensures proper scaling:

```css
.article-link-container {
    width: 100%;
    aspect-ratio: 16 / 18; /* Preserve aspect ratio instead of fixed height */
    height: auto;
}

.article-card-image {
    width: 100%;
    height: 100%;
    aspect-ratio: 16 / 18; /* Match container aspect ratio */
    overflow: hidden;
    border-radius: 4px;
}

/* Adjust aspect ratio at different breakpoints */
@media (max-width: 1200px) {
    .article-card-image, .article-link-container {
        aspect-ratio: 16 / 16;
    }
}

@media (max-width: 767px) {
    .article-card-image, .article-link-container {
        aspect-ratio: 16 / 14;
    }
}

@media (max-width: 576px) {
    .article-card-image, .article-link-container {
        aspect-ratio: 16 / 12;
    }
}
```

### Fluid Typography Scaling

We implemented fluid typography for article content that scales proportionally with viewport size:

```css
.article-card-title {
    font-size: var(--article-title-size);
}

.article-card-excerpt {
    font-size: var(--article-excerpt-size);
}

.article-category, .article-date {
    font-size: var(--article-meta-size);
}
```

### Ghost CMS Integration

We included comprehensive comments for Ghost CMS integration of the fluid article grid:

```html
<!-- Ghost CMS Conversion Note: Responsive Grid Layout -->
<!-- 
The article grid uses a responsive layout that shows:
- 4 articles per row on large screens (1440px+)
- 3 articles per row on medium-large screens (992px-1439px)
- 2 articles per row on medium screens (768px-991px)
- 1 article per row on small screens (<768px)
-->

<!-- Ghost CMS Conversion Note for Article Grid -->
<!-- 
To convert to Ghost CMS template, replace the article grid section with:

<div class="article-grid">
    {{#foreach posts}}
    <article class="article-card">
        <!-- Content structure with Handlebars syntax -->
    </article>
    {{/foreach}}
</div>
-->
```

### Benefits of This Approach

1. **True Fluid Scaling**: The grid expands with the viewport, properly utilizing the entire screen width on large monitors.
2. **Efficient CSS**: Using CSS variables centralizes control of spacing, sizing, and typography.
3. **Proportional Scaling**: All elements maintain correct proportions across screen sizes.
4. **Optimized Mobile Experience**: Content reflows to provide optimal reading experience on smaller devices.
5. **Development Flexibility**: The system is easily modifiable by adjusting a few central variables.
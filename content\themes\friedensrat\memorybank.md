# Friedensrat Ghost Theme Documentation

This document serves as a reference for the Friedensrat Ghost theme, which was converted from a static HTML template.

## Theme Structure

```
friedensrat/
├── assets/
│   ├── css/
│   │   ├── fonts.css
│   │   ├── fluid-typography.css
│   │   ├── main.css
│   │   ├── mobile-nav.css
│   │   ├── normalize.css
│   │   ├── responsive.css
│   │   └── style.css
│   ├── fonts/
│   │   └── [Atkinson Hyperlegible Next font files]
│   ├── images/
│   │   ├── article-images/
│   │   └── team/
│   └── js/
│       ├── main.js
│       └── mobile-nav.js
├── partials/
│   ├── mobile-navigation.hbs
│   ├── site-footer.hbs
│   └── site-header.hbs
├── author.hbs
├── default.hbs
├── index.hbs
├── page.hbs
├── post.hbs
├── tag.hbs
└── package.json
```

## Template Files

### default.hbs
The base template that contains the HTML structure, head, and body tags. It includes the required Ghost helpers and loads the necessary CSS and JavaScript files.

### index.hbs
The homepage template that displays the latest posts, highlights, events, team members, and a newsletter subscription form.

### post.hbs
The single post template that displays the post content, metadata, related posts, and comments.

### page.hbs
The template for static pages.

### tag.hbs
The template for tag archives.

### author.hbs
The template for author archives.

## Partials

### mobile-navigation.hbs
The mobile navigation menu that appears on smaller screens.

### site-header.hbs
The site header that includes the logo, navigation menu, and header content.

### site-footer.hbs
The site footer that includes contact information, navigation links, and copyright information.

## Custom Features

### Article Slider
The homepage features an article slider that displays the latest posts. It uses JavaScript to enable horizontal scrolling.

### Highlight Sections
The highlight sections on the homepage are dynamically populated from Pages with the "homepage-section" tag:
- "homepage-highlight-1" page: First highlight section (reverse layout)
- "homepage-highlight-2" page: Second highlight section (mobile-flip layout)
Each highlight page uses its title, content, and featured image.

### Team Section
The team section is dynamically populated from:
- A "homepage-team-section" page: Provides the section title and quote content
- Posts with the "homepage-team-member" tag: Displayed as team members with their portraits and information
The section uses JavaScript to switch between team members.

### Events Section
The events section displays upcoming events. It uses the {{#get}} helper to fetch posts with the "homepage-event" tag.

### Newsletter Subscription
The newsletter subscription form uses Ghost's membership system to allow visitors to subscribe to the newsletter.

## CSS Organization

The CSS is organized into multiple files:

- **normalize.css**: Resets browser styles
- **fonts.css**: Font declarations
- **style.css**: Component-specific styles
- **fluid-typography.css**: Fluid typography system
- **main.css**: Base site-wide styles
- **responsive.css**: Media queries and responsive styles
- **mobile-nav.css**: Mobile navigation styles

## JavaScript

- **main.js**: Main JavaScript functionality for the article slider, team section, etc.
- **mobile-nav.js**: Mobile navigation functionality

## Ghost Helpers Used

- **{{asset}}**: For referencing theme assets
- **{{body_class}}**: For adding dynamic classes to the body tag
- **{{ghost_head}}**: For adding necessary Ghost scripts to the head
- **{{ghost_foot}}**: For adding necessary Ghost scripts before closing body
- **{{#foreach}}**: For looping through posts
- **{{content}}**: For outputting post content
- **{{excerpt}}**: For outputting post excerpts
- **{{title}}**: For outputting post titles
- **{{date}}**: For formatting dates
- **{{tags}}**: For displaying post tags
- **{{url}}**: For generating post URLs
- **{{img_url}}**: For generating image URLs
- **{{navigation}}**: For outputting site navigation
- **{{#is}}**: For checking the current context
- **{{#has}}**: For checking if a post has certain attributes
- **{{#get}}**: For custom queries

## Customization

The theme can be customized through Ghost's admin interface:

1. **Navigation**: Edit the navigation menu in the Ghost admin under "Settings" > "Navigation"
2. **Site Title and Description**: Edit the site title and description in the Ghost admin under "Settings" > "General"
3. **Logo**: Upload a logo in the Ghost admin under "Settings" > "Design"
4. **Social Media Links**: Add social media links in the Ghost admin under "Settings" > "General"

## Content Creation

When creating content for this theme, consider the following:

1. **Featured Images**: Use featured images for posts to display in the article slider and post cards
2. **Tags**: Use tags to categorize posts and display them in the appropriate sections:
   - `homepage-section`: For pages that should appear in homepage sections
   - `homepage-team-member`: For posts that should appear in the team section on the homepage
   - `homepage-event`: For posts that should appear in the events section on the homepage
3. **Slugs**: Use specific slugs for special pages:
   - `homepage-highlight-1`: For the first highlight section on the homepage
   - `homepage-highlight-2`: For the second highlight section on the homepage
   - `homepage-team-section`: For the team section page on the homepage
4. **Custom Excerpts**: Add custom excerpts to posts for better display in post cards
5. **Authors**: Add author information to display in the author archives

See the `client-guide.md` file for detailed instructions on creating and editing content.

## Maintenance

To update the theme:

1. Make changes to the theme files
2. Restart Ghost with `ghost restart`
3. Test the changes in the browser

## Credits

- Original design: Schweizerischer Friedensrat
- Font: Atkinson Hyperlegible Next
- Development: Ghost Converter

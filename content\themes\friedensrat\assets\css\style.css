

/* Header styles */
.header {
    width: 100%;
    display: flex;
    justify-content: stretch;
    align-items: stretch;
    margin-bottom: var(--section-spacing);
}

.header-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    gap: 5.625rem;
    padding: 2.5rem var(--inner-padding);
    background-image: url('../img/header-background.jpg');
    background-size: cover;
    background-position: center;
    position: relative;
    border-radius: 0.5rem;
    height: 85vh; /* Increased from 70vh to 85vh */
    min-height: 700px; /* Also increased min-height for smaller screens */
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.header-wrapper::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.1);
    z-index: 1;
    border-radius: 0.5rem;
}

.nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 2;
    width: 100%;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo-link {
    display: block;
}

.logo-svg {
    height: auto;
    width: clamp(15rem, 10vw + 8rem, 18.75rem); /* Fluid width: min 160px, max 300px */
    filter: brightness(0) invert(1); /* Makes SVG white */
}

/* Hide visually but keep accessible for screen readers */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

.logo-text {
    font-family: 'Albert Sans', sans-serif;
    font-weight: 700;
    line-height: 0.85;
    text-transform: uppercase;
    color: var(--white);
}

.menu {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 1.5625rem;
}

.menu a {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 500;
    line-height: 2;
    letter-spacing: -1.6%;
    color: var(--white);
    transition: color 0.3s ease, transform 0.2s ease;
    position: relative;
}

.menu a:hover {
    color: rgba(255, 255, 255, 0.8);
    transform: translateY(-2px);
}

.menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--white);
    transition: width 0.3s ease;
}

.menu a:hover::after {
    width: 100%;
}

/* Desktop Dropdown Styles */
.menu li {
    position: relative; /* Parent for absolute dropdown */
}

/* Remove styles for span indicator */
/* .menu li.has-dropdown > a .dropdown-indicator { ... } */

/* Styles for SVG indicator */
.menu li.has-dropdown > a .dropdown-indicator-svg {
    display: inline-block;
    width: 0.6em; /* Adjust size */
    height: 0.6em;
    margin-left: 0.4em; /* Adjust spacing */
    vertical-align: middle; /* Align with text */
    /* opacity: 0.7; */ /* Filter replaces need for opacity */
    transition: transform 0.3s ease;
    filter: brightness(0) invert(1); /* Make SVG white */
}

.menu li.has-dropdown:hover > a .dropdown-indicator-svg {
    transform: rotate(180deg);
}

.menu ul.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--white);
    border-radius: 0.25rem;
    padding: 0.5rem 0; /* Vertical padding */
    margin: 0.5rem 0 0 0; /* Add slight gap */
    list-style: none;
    min-width: 180px;
    z-index: 100;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0,0,0,0.05);

    /* Initial hidden state */
    visibility: hidden;
    opacity: 0;
    transform: translateY(10px);
    transition: visibility 0s linear 0.3s, opacity 0.3s ease, transform 0.3s ease;
}

.menu li.has-dropdown:hover > ul.dropdown-menu {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0s; /* Remove delay for showing */
}

.menu ul.dropdown-menu li {
    margin: 0;
}

.menu ul.dropdown-menu li a {
    display: block;
    padding: 0.5rem 1rem; /* Padding for dropdown items */
    color: var(--text-dark);
    font-size: 0.95rem; /* Slightly smaller font */
    white-space: nowrap;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.menu ul.dropdown-menu li a:hover {
    background-color: #f0f0f0;
    color: var(--dark-blue);
}

/* Remove hover underline effect from main menu item if it has dropdown? Optional */
.menu li.has-dropdown > a:hover::after {
    width: 0;
}

/* Add styles for the mobile nav structure itself */
.navigation {
    display: none; /* Hide mobile nav component by default */
}

/* Mobile Nav Toggle Button Styling */
.mobile-nav-toggle {
    display: none; /* Hidden by default */
    flex-direction: column;
    justify-content: space-around; /* Space out bars */
    width: 2.5rem; /* Size of the toggle */
    height: 2rem;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 10; /* Ensure it's above header content */
}

.mobile-nav-toggle:focus {
    outline: none;
}

.mobile-nav-toggle__bar {
    width: 1.875rem; /* Width of bars */
    height: 3px; /* Thickness of bars */
    background: var(--white); /* Color of bars */
    border-radius: 10px;
    transition: all 0.3s linear;
    position: relative;
    transform-origin: 1px; /* Helps with rotation */
}

/* Add animation styles later if using the JS toggle animation */

.header-column {
    position: absolute;
    bottom: 2.5rem;
    left: var(--inner-padding);
    z-index: 2;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: var(--dark-blue);
    padding: 3.125rem;
    border-radius: 0.5rem;
    max-width: 43.875rem; /* 702px */
    width: calc(100% - 2 * var(--inner-padding)); /* Fluid width with inner padding accounted for */
    min-height: 70%;
    /*min-height: 26.8125rem;*/ /* 429px */
}

.header-column-content {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.header-column-content h1 {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 600;
    line-height: 1.09;
    color: var(--white);
    margin: 0;
    letter-spacing: -0.02em;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.header-column-content p {
    font-family: 'Albert Sans', sans-serif;
    font-weight: 400;
    line-height: 1.5;
    color: var(--white);
    margin: 0;
    letter-spacing: -0.01em;
}

.header-column-link {
    margin-top: 2rem;
    display: flex;
    justify-content: flex-start;
}

.btn {
    display: flex;
    align-items: center;
    gap: 0.625rem;
    padding: 0.4rem 0.9375rem;
    background-color: var(--white);
    border-radius: 0.25rem;
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 500;
    letter-spacing: -1.61%;
    color: var(--dark-blue);
    transition: all 0.3s ease;
}

.btn:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.arrow {
    display: flex;
    align-items: center;
    margin-left: 0.3125rem;
    transition: transform 0.3s ease;
}

.btn:hover .arrow {
    transform: translateX(5px);
}

.arrow-part {
    height: 0.9375rem;
}



/* Article Slider Section */
.article-slider {
    width: 100%;
    margin-bottom: var(--section-spacing);
    padding: 0;
    position: relative;
    border-bottom: 1px solid rgba(20, 21, 26, 0.1); /* Add subtle divider line */
    padding-bottom: var(--section-spacing); /* Add padding below the slider content before the border */
}

.slider-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 2.3rem; /* ~36.9px */
    padding-left: var(--inner-padding);
}

.slider-heading {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 500;
    line-height: 0.75;
    letter-spacing: -3.24%;
    text-align: left;
    color: var(--text-dark);
    margin: 0;
    font-feature-settings: "salt" 1, "ss01" 1;
}

.slider-grid {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    gap: 1.28rem; /* ~20.5px */
}

.article-grid {
    width: 100%;
    overflow-x: auto;
    scrollbar-width: none; /* For Firefox */
    -ms-overflow-style: none; /* For Internet Explorer and Edge */
    padding-right: 0; /* Allow overflow on the right */
    margin-right: -0.5rem; /* Compensate for the container padding */
}

.article-grid::-webkit-scrollbar {
    display: none; /* For Chrome, Safari, and Opera */
}

.grid-wrapper {
    display: flex;
    gap: 1.92rem; /* ~30.7px */
    padding-bottom: 1rem; /* Space for scrollbar if needed */
}

.article {
    display: flex;
    flex-direction: column;
    gap: 1rem; /* ~15.4px */
    width: 18.45rem; /* ~295.2px */
    flex-shrink: 0;
}

.article-link {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.article-image-container {
    width: 100%;
    border-radius: 0.25rem; /* 4px */
    overflow: hidden;
    transition: all 0.3s ease;
}

/* Base height for all article images */
.article-image-container {
    height: 12.5rem; /* ~200px - default height */
}

/* Alternating pattern for article images with more dramatic height differences */
.article:nth-child(1) .article-image-container,
.article:nth-child(5) .article-image-container {
    height: 12.5rem; /* ~200px - standard height */
}

.article:nth-child(2) .article-image-container,
.article:nth-child(6) .article-image-container {
    height: 16rem; /* ~256px - taller */
}

.article:nth-child(3) .article-image-container,
.article:nth-child(7) .article-image-container {
    height: 25rem; /* ~400px - double height for dramatic effect */
}

.article:nth-child(4) .article-image-container,
.article:nth-child(8) .article-image-container {
    height: 18rem; /* ~288px - taller */
}

.article-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    display: block;
}

/* Apply transform only to the image on hover */
.article-image-container:hover .article-image {
    transform: scale(1.05);
}

.article-content {
    display: flex;
    flex-direction: column;
    gap: 1rem; /* ~15.4px */
}

.article-meta {
    display: flex;
    flex-direction: column;
    gap: 1.1rem; /* ~17.4px */
}

.article-date {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    font-size: 0.6rem; /* ~9.8px */
    line-height: 1.9;
    letter-spacing: 3.26%;
    text-transform: uppercase;
    color: var(--text-dark);
    font-feature-settings: "tnum" 1, "lnum" 1;
}

.article-title {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 600;
    line-height: 1.01;
    letter-spacing: -1%;
    color: var(--text-dark);
    margin: 0;
    font-feature-settings: "salt" 1;
    text-shadow: 0 0.5px 0 rgba(255, 255, 255, 0.8);
}

/* Article category styles moved to common styles section with event-tag */

/* Category colors */
.friedenszeitung,
.konferenz {
    background-color: #D84406; /* Orange */
}

.artikel,
.diskussion {
    background-color: #12154B; /* Dark blue */
}

.internes,
.wirtschaft,
.gedenkveranstaltung {
    background-color: #5BDEFF; /* Light blue */
}

/* Default for any event tag without a specific color class */
.event-tag {
    background-color: var(--dark-blue); /* Default to dark blue */
}

/* Default text color for event tags */
.event-tag,
.event-tag span {
    color: #FFFFFF; /* Default to white text for all event tags */
    text-decoration: none;
}

/* Text colors based on background - for article categories */
.friedenszeitung,
.friedenszeitung span,
.artikel,
.artikel span,
.diskussion,
.diskussion span,
.konferenz,
.konferenz span {
    color: #FFFFFF;
    text-decoration: none;
}

/* Light background tags get dark text */
.internes,
.internes span,
.wirtschaft,
.wirtschaft span {
    color: var(--text-dark);
    text-decoration: none;
}

/* Specific override for light-colored event tags */
.event-tag.gedenkveranstaltung,
.event-tag.gedenkveranstaltung span {
    color: var(--text-dark);
    text-decoration: none;
}

/* Slider controls */
.slider-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1.86rem; /* ~29.7px */
    margin-left: var(--inner-padding);
}

.slider-arrow {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 2.74rem; /* ~27.9px */
    height: 2.74rem; /* ~27.9px */
    border: 1px solid var(--text-dark);
    border-radius: 50%;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0;
}

.slider-prev {
    opacity: 0.4; /* As in the design */
}

.slider-arrow:hover {
    background-color: var(--text-dark);
}

.slider-arrow:hover img {
    filter: brightness(0) invert(1);
}

/* Specific styling for slider arrows only */
.slider-arrow .arrow-icon {
    width: 40%;
}

/* Specific styling for CTA button arrow */
.btn .arrow-icon {
    height: 0.9375rem;
}


/* Friedensrat Highlights Section */
.friedensrat-highlights {
    width: 100%;
    margin-bottom: var(--section-spacing);
    padding: 0 0.5rem;
}

.columns {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    width: 100%;
    column-gap: var(--gutter);
    --columns: 12;
}

.col-12 {
    width: 100%;
    margin-bottom: 1.5rem;
}

.cards-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: minmax(calc((100vw - var(--pageMarginTotal) * 2 - var(--gutter)) / 2), auto);
    gap: var(--gutter);
}

/* First card has content on left, image on right */
.cards-row:not(.mobile-flip) {
    grid-template-areas: "content image";
}

/* Second card has image left, content right on DESKTOP */
.cards-row.mobile-flip {
    grid-template-areas: "image content"; /* Reverted to image left */
}

.card-content {
    grid-area: content;
    height: 100%;
    background-color: #F5F5F5;
    border-radius: 0.75rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 3.125rem;
    max-height: var(--maxContentHeight);
    gap: 1.4rem;
}

.top,
.highlight-description {
    max-width: 50%;
}

.card-image {
    grid-area: image;
    border-radius: 0.75rem;
    overflow: hidden;
    max-height: var(--maxContentHeight);
    position: relative;
}

.highlight-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
}

.top h3 {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 500;
    line-height: 1.06;
    letter-spacing: -2%;
    color: var(--black);
    margin: 0;
}

.highlight-description {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.highlight-description p {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    line-height: 1.24;
    letter-spacing: -0.99%;
    color: rgba(0, 0, 0, 0.7);
    margin: 0;
}



/* Events Section */
.events-section {
    width: 100%;
    margin-bottom: var(--section-spacing);
    padding: 0 0.5rem;
}

.events-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    padding: 0 var(--inner-padding);
}

.events-heading {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 500;
    line-height: 1.2;
    letter-spacing: -3%;
    color: var(--text-dark);
    margin: 0;
}

.view-all-link {
    display: inline-block;
    background-color: var(--dark-blue);
    color: var(--white);
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 500;
    font-size: 0.85rem;
    line-height: 1.2;
    text-transform: uppercase;
    padding: 0.625rem 0.9375rem;
    border-radius: 0.25rem;
    transition: background-color 0.3s ease;
}

.view-all-link:hover {
    background-color: rgba(18, 21, 75, 0.9);
}

.events-grid {
    display: grid;
    width: 100%;
    position: relative;
    padding: 0 var(--inner-padding);
}

.event-item {
    position: relative;
    padding: 2.375rem;
    min-height: 30rem;
    border-bottom: var(--border-width) solid #000;
    transition: background-color 0.3s ease;
}

.event-item:hover {
    background-color: #C3C3FF;
}

.event-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    gap: 1.75rem;
}

.event-top {
    display: flex;
    flex-direction: column;
    gap: 1.75rem;
}

.event-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.event-date {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 500;
    line-height: 1.28;
    text-transform: uppercase;
    color: var(--text-dark);
}

/* Common styles for both article-category and event-tag */
.article-category,
.event-tag {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.25rem 0.7rem;
    border-radius: 92rem; /* Very large value for pill shape */
    align-self: flex-start;
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    font-size: 0.64rem; /* ~10.16px */
    line-height: 1.84;
    letter-spacing: 3.16%;
    text-transform: uppercase;
    font-feature-settings: "case" 1, "cpsp" 1;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.article-category:hover,
.event-tag:hover {
    opacity: 0.8;
}

.article-category span,
.event-tag span {
    font-family: inherit;
    font-weight: inherit;
    font-size: inherit;
    line-height: inherit;
    letter-spacing: inherit;
    text-transform: inherit;
    font-feature-settings: inherit;
}

.event-title {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 500;
    line-height: 1.1;
    letter-spacing: -2%;
    color: var(--black);
    margin: 0;
}

.event-title-link {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.event-title-link:hover {
    color: var(--dark-blue);
}

.event-description p {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    line-height: 1.3;
    letter-spacing: 1%;
    color: var(--black);
    margin: 0;
}

.event-link {
    display: block;
    width: 100%;
    height: 100%;
    color: inherit;
    text-decoration: none;
}

/* Responsive styles for events section */
@media (min-width: 768px) {
    .events-grid {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .event-item {
        padding: 2.375rem;
        border-bottom: none;
    }

    .event-item:nth-child(3n+1) {
        border-left: var(--border-width) solid #000;
    }

    .event-item {
        border-right: var(--border-width) solid #000;
    }
}

@media (max-width: 768px) {
    .events-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .events-grid {
        grid-template-columns: 1fr;
        padding-left: 0;
        padding-right: 0;
    }

    .event-item {
        min-height: 20rem;
    }

    .event-item:last-child {
        border-bottom: none;
    }
}

/* Offers Section */
.offers-section {
    width: 100%;
    margin-bottom: var(--section-spacing);
    padding: 0;
    overflow: hidden;
}

.offers-headline {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    margin-bottom: 2.5rem;
}

.offers-headline h2 {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 500;
    font-size: 21vw; /* Dynamic font size based on viewport width */
    line-height: 1;
    letter-spacing: -2.26%;
    color: #282828;
    margin: 0;
    width: 100%;
    white-space: nowrap;
    text-align: center;
    overflow: visible;
}

.offers-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    padding: 0 0.5rem;
}

.offers-item {
    display: flex;
    flex-direction: column;
}

.offer-link {
    display: flex;
    flex-direction: column;
    height: 100%;
    text-decoration: none;
    color: inherit;
}

.offer-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 1.5rem;
    border-radius: 0.5rem;
    min-height: 38rem;
    transition: background-color 0.3s ease;
}

/* Hover effects for each offer item */
.offers-item:nth-child(1) .offer-content {
    background-color: #D84406;
}

.offers-item:nth-child(1) .offer-content:hover {
    background-color: #EF6126;
}

.offers-item:nth-child(2) .offer-content {
    background-color: #A6369B;
}

.offers-item:nth-child(2) .offer-content:hover {
    background-color: #BA50B0;
}

.offers-item:nth-child(3) .offer-content {
    background-color: #F09000;
}

.offers-item:nth-child(3) .offer-content:hover {
    background-color: #F9A21F;
}

.offer-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.offer-label {
    display: flex;
    border: 0.75px solid #FFFFFF;
    border-radius: 75px;
    padding: 0.5rem 1.5rem;
}

.offer-label span {
    font-family: 'PP Mori', 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    font-size: 0.875rem;
    line-height: 1.4;
    color: #FFFFFF;
}

.offer-icon {
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-img {
    width: 100%;
    height: auto;
}

.offer-description {
    margin-top: auto;
    max-width: 75%;
}

.offer-description p {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    font-size: 1.25rem;
    line-height: 1.06;
    letter-spacing: -0.84%;
    color: #FFFFFF;
}

.offer-title {
    padding: 1.5rem 3rem 0.75rem 0;
    max-width: 75%;
}

.offer-title h3 {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 500;
    font-size: 2.5rem;
    line-height: 0.7;
    letter-spacing: -2%;
    color: #000000;
    margin: 0;
}

/* Responsive styles for offers section */
@media (max-width: 1200px) {
    .offers-headline h2 {
        font-size: 12.5rem;
    }

    .offer-content {
        min-height: 32rem;
    }

    .offer-title h3 {
        font-size: 2rem;
    }
}

@media (max-width: 992px) {
    .offers-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .offers-headline h2 {
        font-size: 10rem;
    }

    .offer-description p {
        font-size: 1.125rem;
    }
}

@media (max-width: 768px) {
    .offers-grid {
        grid-template-columns: 1fr;
        padding: 0;
    }

    .offers-headline h2 {
        font-size: 5rem;
    }

    .offer-content {
        min-height: 28rem;
    }

    .offer-title h3 {
        font-size: 1.75rem;
    }
}

/* Team Section */
.team-section {
    width: 100%;
    margin-bottom: 0.625rem;
    padding: 90px 0.5rem 0 0.5rem;
}

.team-wrapper {
    display: grid;
    grid-template-rows: auto auto;
    row-gap: 1.5rem;
    width: 100%;
    margin: 0 auto;
    border-radius: 0.5rem;
    overflow: hidden;
    padding: 3.125rem;
    background: #F5F5F5 ;
}

.team-content {
    display: flex;
    flex-direction: column;
    width: 100%;
}

/* Renamed from .quote-wrapper */
.team-main-content {
    display: grid; /* Apply grid here */
    grid-template-rows: auto 1fr; /* Header auto, quote takes rest? Or auto auto? */
    /* Let's try auto auto and control gap */
    grid-template-rows: auto auto;
    row-gap: clamp(5rem, 15vw, 19rem); /* Fluid gap between header and quote */
    width: 100%;
    /* Removed min-height */
    /* Removed justify-content */
    /* Removed internal gap flex property */
}

.team-header {
    /* Styles for the header grid item (row 1) */
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    gap: 28px;
}

.team-heading {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 500;
    line-height: 1.2;
    letter-spacing: -1%;
    color: #000000;
    margin: 0;
}

.team-cta-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    background-color: #FFFFFF;
    color: #282828;
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    line-height: 1.18;
    letter-spacing: -2%;
    padding: 10px 15px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
    text-decoration: none;
}

.team-cta-button:hover {
    background-color: rgba(255, 255, 255, 0.9);
}

.quote-content {
    /* Styles for the quote grid item (row 2) */
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 70%;
}

.quote-text {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    font-style: normal;
    line-height: 1.15;
    letter-spacing: -2%;
    color: #282828;
    margin: 0;
    transition: opacity 0.3s ease;
}

/* Renamed from .team-img-select */
.team-selection-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    /* Removed border-top/padding-top */
}

.team-select {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 23px;
    position: relative;
}

.team-portraits {
    display: flex;
    flex-wrap: wrap;
    gap: 11.6px;
    margin-bottom: 0;
    max-width: none;
}

.team-member-portrait {
    width: 71px;
    height: 71px;
    position: relative;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    opacity: 0.6;
    transition: opacity 0.3s ease, transform 0.2s ease;
}

.team-member-portrait.active {
    opacity: 1;
    transform: scale(1.05);
}

.team-member-portrait:hover {
    opacity: 0.8;
}

.team-member-portrait.active:hover {
    opacity: 1;
}

.portrait-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.team-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    max-width: none;
    margin-left: 23px;
}

.member-name {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    line-height: 1.2;
    letter-spacing: -1.27%;
    color: #282828;
}

.member-title {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    line-height: 1.2;
    letter-spacing: -1.27%;
    color: #282828;
    opacity: 0.5;
}

.team-logo {
    position: static;
    align-self: flex-end;
    width: 60px;
    height: 60px;
    margin-left: auto;
}

.logo-small {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Responsive styles for team section */
@media (max-width: 992px) {
    .team-wrapper {
        padding: 40px 30px;
    }

    .team-main-content {
        gap: 160px;
    }

    .quote-text {
        /* font-size: 36px; */ /* Removed fixed size */
    }

    .team-selection-area {
        flex-direction: column;
        align-items: flex-start;
        gap: 30px;
    }

    .team-select {
        width: 100%;
    }

    .team-info {
        margin-left: 10px;
    }

    .team-logo {
        align-self: flex-end;
        margin-top: 20px;
    }
}

@media (max-width: 768px) {
    .team-wrapper {
        padding: 30px 20px;
    }

    .team-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .team-main-content {
        gap: 100px;
    }

    .quote-text {
        /* font-size: 28px; */ /* Removed fixed size */
        line-height: 1.2;
    }

    .team-portraits {
        flex-wrap: wrap;
        gap: 10px;
    }

    .team-member-portrait {
        width: 60px;
        height: 60px;
    }
}

@media (max-width: 576px) {
    .team-wrapper {
        padding: 25px 15px;
    }

    .team-main-content {
        gap: 60px;
    }

    .quote-text {
        /* font-size: 24px; */ /* Removed fixed size */
    }

    .team-member-portrait {
        width: 50px;
        height: 50px;
    }

    .team-cta-button {
        /* font-size: 18px; */ /* Removed fixed size */
        padding: 8px 12px;
    }

    .team-logo {
        width: 45px;
        height: 45px;
    }
}

/* Newsletter Section */
.newsletter-section {
}

.newsletter-wrapper {
    background-color: var(--dark-blue);
    border-radius: 0.5rem;
    padding: 3.125rem;
    width: 100%;
}

.newsletter-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 1.875rem;
    width: 100%;
}

.newsletter-heading {
    color: var(--white);
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    font-size: 2.82rem; /* 45.15px */
    line-height: 1.01;
    letter-spacing: -1.05%;
    margin: 0;
    flex-shrink: 0;
}

.newsletter-form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 0.6875rem; /* 11px */
    width: 100%;
}

.form-background {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.newsletter-form-element {
    display: flex;
    flex-direction: row;
    gap: 1.25rem; /* 20px */
    width: 100%;
}

.form-input {
    display: flex;
    background-color: var(--white);
    padding: 0.825rem 0.55rem; /* 13.2px 8.8px */
    border-radius: 0.25rem; /* 4px */
    flex-grow: 1;
}

.form-input input {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 300;
    font-size: 1.125rem; /* 18px */
    line-height: 1.157;
    color: var(--gray);
    border: none;
    outline: none;
    width: 100%;
    background: transparent;
}

.newsletter-submit-btn {
    background-color: var(--white);
    color: #282828;
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    font-size: 1.468rem; /* 23.49px */
    line-height: 1.18;
    letter-spacing: -2.02%;
    padding: 0.625rem 0.9375rem; /* 10px 15px */
    border: none;
    border-radius: 0.25rem; /* 4px */
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: background-color 0.3s ease;
}

.newsletter-submit-btn:hover {
    background-color: #f0f0f0;
}

.privacy-notice {
    display: flex;
    flex-direction: row;
    align-items: center; /* Align items vertically in the center */
    gap: 0.3125rem; /* 5px */
    margin-top: 0.5rem;
}

.privacy-text {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    font-size: 1.125rem; /* 18px */
    line-height: 1.25;
    color: var(--white);
    margin: 0; /* Remove default paragraph margin */
}

.privacy-link {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 500;
    font-size: 1.125rem; /* 18px */
    line-height: 1.25;
    color: var(--white);
    text-decoration: underline;
    transition: opacity 0.3s ease;
    display: inline-block; /* Ensure proper box model */
}

.privacy-link:hover {
    opacity: 0.8;
}

/* Newsletter success and error messages */
.newsletter-success-message,
.newsletter-error-message {
    display: none;
    margin-top: 1rem;
    padding: 0.75rem;
    border-radius: 0.25rem;
    text-align: center;
}

.newsletter-success-message {
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--dark-blue);
}

.newsletter-error-message {
    background-color: rgba(255, 200, 200, 0.9);
    color: #d84406;
}

/* These classes are added by Ghost when the form is submitted */
.success .newsletter-success-message {
    display: block;
}

.error .newsletter-error-message {
    display: block;
}

/* Responsive styling for newsletter section */
@media (max-width: 992px) {
    .newsletter-container {
        flex-direction: column;
        align-items: flex-start;
    }

    .newsletter-heading {
        margin-bottom: 1.25rem;
    }

    .newsletter-form-element {
        flex-direction: column;
    }

    .newsletter-submit-btn {
        align-self: flex-start;
    }
}

@media (max-width: 768px) {
    .newsletter-wrapper {
        padding: 2.5rem 1.5rem;
    }

    .newsletter-heading {
        font-size: 2.25rem;
    }

    .form-input {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .newsletter-wrapper {
        padding: 2rem 1rem;
    }

    .privacy-notice {
        flex-direction: column;
    }
}

/* Footer Section Styles */
.footer-section {
    width: 100%;
    background-color: #E0F2FF; /* Light blue background from Figma */
    color: var(--text-dark);
}

.footer-container {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: 0 var(--inner-padding);
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 4rem;
}

/* Footer navigation styles */
.footer-nav {
    display: flex;
    gap: 2.5rem;
}

.footer-nav-column {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-nav-column h3 {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 500;
    font-size: 1.25rem;
    line-height: 1.2;
    color: var(--dark-blue);
    margin: 0 0 0.75rem 0;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.65rem;
}

.footer-links li a {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-dark);
    text-decoration: none;
    transition: color 0.2s ease;
    position: relative;
}

.footer-links li a:hover {
    color: var(--dark-blue);
}

.footer-links li a::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 0;
    height: 1px;
    background-color: var(--dark-blue);
    transition: width 0.2s ease;
}

.footer-links li a:hover::after {
    width: 100%;
}

/* Footer info styles */
.footer-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.footer-logo {
    max-width: 200px;
}

.footer-logo-img {
    width: 100%;
    height: auto;
}

.footer-address {
    font-style: normal;
}

.footer-address p {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    font-size: 0.9375rem;
    line-height: 1.6;
    color: var(--text-dark);
    margin: 0 0 1rem 0;
}

.footer-address a {
    color: var(--dark-blue);
    text-decoration: none;
    transition: color 0.2s ease;
}

.footer-address a:hover {
    color: rgba(18, 21, 75, 0.8);
    text-decoration: underline;
}

/* Footer donate styles */
.footer-donate {
    display: flex; /* Keep flex for alignment */
    position: relative; /* Needed for absolute positioning of the button */
    justify-content: center; /* Center flex items (SVG in this case) */
    align-items: center; /* Center flex items */
    width: 100%; /* Ensure it takes full width */
    padding: 2rem 0; /* Add some vertical padding */
    min-height: 10rem; /* Give it some minimum height */
    overflow: hidden; /* Hide parts of SVG that might overflow */
}

/* Styles for the SVG graphic */
.donate-svg-graphic {
    display: block; /* Remove extra space below image */
    width: 100%; /* Make SVG fill the container width */
    height: auto; /* Maintain aspect ratio */
    max-height: 153px; /* Optional: Limit height based on SVG's viewBox height */
    object-fit: contain; /* Ensure SVG scales nicely within its box */
}

.footer-donate-button {
    position: absolute; /* Position button over the SVG */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); /* Center the button */
    background-color: #FFFFFF;
    color: #282828;
    line-height: 1.18;
    letter-spacing: -0.02em;
    padding: 0.625rem 1rem; /* 10px 15px */
    border-radius: 4px;
    text-decoration: none;
    font-weight: 400; /* Figma uses 400 */
    transition: background-color 0.2s ease, color 0.2s ease, transform 0.2s ease;
    white-space: nowrap; /* Prevent button text wrapping */
    z-index: 1; /* Ensure button is above SVG */
}

.footer-donate-button:hover {
    background-color: #f0f0f0;
    transform: translate(-50%, -50%) scale(1.05); /* Slight scale on hover */
}

.social-media {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: var(--dark-blue);
    transition: background-color 0.2s ease;
}

.social-icon:hover {
    background-color: rgba(18, 21, 75, 0.8);
}

.social-icon img {
    width: 1.25rem;
    height: 1.25rem;
    filter: brightness(0) invert(1); /* Makes SVG white */
}

/* Footer bottom styles */
.footer-bottom {
    padding-top: 2rem;
    padding-bottom: 2rem;
    border-top: 1px solid rgba(18, 21, 75, 0.1);
    margin-top: 4rem;
}

.footer-bottom .footer-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.copyright p {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    font-size: 0.875rem;
    line-height: 1.5;
    color: rgba(18, 21, 75, 0.7);
    margin: 0;
}

.legal-links {
    display: flex;
    gap: 1.5rem;
}

.legal-links a {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    font-size: 0.875rem;
    line-height: 1.5;
    color: rgba(18, 21, 75, 0.7);
    text-decoration: none;
    transition: color 0.2s ease;
}

.legal-links a:hover {
    color: var(--dark-blue);
    text-decoration: underline;
}

/* Responsive styles for footer */
@media (max-width: 992px) {
    .footer-container {
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
        padding-top: 3rem;
    }

    .footer-nav {
        grid-column: span 2;
    }

    .footer-donate {
        min-height: 8rem; /* Adjust min height */
        padding: 1.5rem 0;
    }
}

@media (max-width: 768px) {
    .footer-container {
        grid-template-columns: 1fr;
        gap: 2.5rem;
        padding-top: 2.5rem;
    }

    .footer-nav {
        grid-column: span 1;
        flex-direction: column;
        gap: 2rem;
    }

    .footer-donate {
        min-height: 6rem; /* Adjust min height */
        padding: 1rem 0;
    }

    /* Button size might need adjustment */
     .footer-donate-button {
        padding: 0.5rem 0.8rem;
     }

    .footer-bottom .footer-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .copyright {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 480px) {
    .footer-nav {
        flex-direction: column; /* Stack nav columns */
        gap: 1.5rem;
    }

    .footer-nav-column {
        flex-basis: 100%; /* Full width */
    }

    .footer-donate {
        min-height: 5rem; /* Adjust min height */
    }

     .footer-donate-button {
        padding: 0.4rem 0.7rem;
     }
}

/* --- Footer Section --- */
.footer {
    background-color: #C3C3FF; /* Match Figma background */
    padding-top: 3.125rem; /* 50px */
    padding-bottom: 3.125rem; /* 50px */
    border-top-left-radius: 0.5rem; /* Match header rounding */
    border-top-right-radius: 0.5rem;
    margin-top: 0.625rem;
}

.footer-wrapper {
    display: flex;
    flex-direction: column;
    gap: 2.3rem; /* ~37px from Figma */
    max-width: var(--max-width);
    margin: 0 auto;
    padding-left: var(--inner-padding);
    padding-right: var(--inner-padding);
}

.footer-content-top {
    display: flex;
    flex-direction: column;
    gap: 2.625rem; /* 42px from Figma - gap between row and donate */
    padding-bottom: 2.625rem; /* Add padding below top content */
    border-bottom: 1px solid rgba(20, 21, 26, 0.1); /* Subtle separator */
}

/* New styles for the row containing company data and nav */
.footer-top-row {
    display: flex;
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
    justify-content: space-between;
    gap: 2rem; /* Gap between company data and nav */
}

.footer-company-data {
    display: flex;
    flex-direction: row; /* Logo and info side-by-side */
    align-items: flex-start; /* Align items to top */
    gap: 2rem; /* 32px - space between logo and info */
    flex-basis: 45%; /* Adjust basis as needed */
    min-width: 300px; /* Prevent excessive shrinking */
}

.footer-logo-container {
    max-width: 100px; /* Match Figma logo size */
}

.footer-logo {
    display: block;
    width: 100%;
    height: auto;
}

.footer-info {
    display: flex;
    flex-direction: column;
    gap: 0.9375rem; /* 15px */
}

.footer-org-name {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 500;
    font-size: 1.25rem;
    line-height: 1.2;
    letter-spacing: -0.01em;
    color: #14151A;
    margin: 0;
}

.footer-tagline {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    font-size: 0.875rem;
    line-height: 1.4;
    color: #14151A;
    margin: 0 0 0.5rem 0;
}

.footer-address {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #14151A;
    font-style: normal;
    margin: 0;
}

.footer-address a {
    color: #12154B;
    text-decoration: none;
    transition: color 0.2s ease;
}

.footer-address a:hover {
    color: #000;
    text-decoration: underline;
}

.footer-nav {
    display: flex;
    gap: 2.5rem; /* Increased gap between nav columns */
    flex-basis: 50%; /* Adjust basis */
    justify-content: flex-end; /* Align nav columns to the right */
    flex-grow: 1; /* Allow nav to take available space if company data shrinks */
    flex-wrap: wrap; /* Allow columns to wrap if needed */
}

.footer-nav-column {
    flex-basis: 180px; /* Give columns a base width */
    min-width: 150px; /* Prevent shrinking too much */
}

.footer-nav-column h5 {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 600;
    font-size: 1rem;
    line-height: 1.2;
    color: #14151A;
    margin: 0 0 1rem 0;
}

.footer-nav-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.625rem; /* 10px */
}

.footer-nav-column li a {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    font-size: 0.875rem; /* 14px */
    line-height: 1.4;
    color: #14151A;
    text-decoration: none;
    transition: color 0.2s ease;
}

.footer-nav-column li a:hover {
    color: #000; /* Darken on hover */
    text-decoration: underline;
}

.footer-donate {
    display: flex;
    flex-direction: row; /* Changed from column */
    justify-content: space-between; /* Push heading left, button right */
    align-items: center; /* Align items vertically */
    gap: 1rem; /* 16px */
    width: 100%; /* Ensure it takes full width */
    margin-top: 0; /* Reset margin if any */
    /* Remove flex-basis and min-width as it's a row now */
}

.footer-donate-heading {
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 700;
    line-height: 1;
    letter-spacing: -0.02em;
    text-transform: uppercase;
    color: #FFFFFF;
    margin: 0;
    flex-shrink: 0;
}

.footer-donate-button {
    background-color: #FFFFFF;
    color: #282828;
    line-height: 1.18;
    letter-spacing: -0.02em;
    padding: 0.625rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 400;
    transition: background-color 0.2s ease, color 0.2s ease;
    white-space: nowrap;
    align-self: center;
}

.footer-content-bottom {
    display: flex;
    flex-wrap: wrap; /* Allow wrapping */
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    font-family: 'Atkinson Hyperlegible Next', sans-serif;
    font-weight: 400;
    font-size: 0.75rem; /* 12px */
    line-height: 1.4;
    color: #14151A;
    margin-top: 1rem;
}

.footer-copyright {
    flex-grow: 1;
    text-align: left;
}

.footer-copyright p {
    margin: 0;
}

.footer-legal-links ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap; /* Allow wrapping */
    gap: 1.5rem; /* 24px */
}

.footer-legal-links li a,
.footer-legal-links li button {
    color: #14151A;
    text-decoration: none;
    transition: color 0.2s ease;
    font-size: 0.75rem; /* 12px */
}

.footer-legal-links li a:hover,
.footer-legal-links li button:hover {
    color: #000;
    text-decoration: underline;
}

.footer-legal-links li button {
    background: none;
    border: none;
    padding: 0;
    font: inherit;
    cursor: pointer;
    color: #14151A;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .footer-top-row {
        flex-direction: column; /* Stack company/nav vertically */
        align-items: flex-start;
        gap: 2rem;
    }

    .footer-company-data {
        flex-basis: 100%;
        flex-direction: column; /* Stack logo and info */
        align-items: flex-start;
        gap: 1rem;
    }

    .footer-nav {
        width: 100%; /* Take full width when stacked */
        justify-content: space-between; /* Space out columns */
        flex-basis: auto;
    }

    .footer-nav-column {
        flex-basis: 45%; /* Roughly two columns side-by-side */
        min-width: 150px;
    }

    .footer-donate {
        /* Keep as row, but maybe adjust alignment/size */
        align-items: center;
    }

    .footer-donate-heading {
    }
}

@media (max-width: 768px) {
    .footer-wrapper {
        gap: 1.5rem;
    }

    .footer-company-data {
        gap: 1.5rem; /* Increase gap slightly */
    }

    .footer-donate {
        flex-direction: column; /* Stack heading and button */
        align-items: flex-start;
        gap: 1rem;
    }

     .footer-donate-heading {
    }

    .footer-donate-button {
        align-self: flex-start;
    }

    .footer-content-bottom {
        flex-direction: column; /* Stack copyright and links */
        align-items: flex-start; /* Align all to start */
        gap: 0.8rem;
    }

    .footer-copyright {
        text-align: left;
    }

    .footer-legal-links ul {
        justify-content: flex-start;
    }

    .team-select {
        flex-direction: column; /* Stack portraits and info */
        align-items: flex-start;
        gap: 1rem; /* Add gap when stacked */
    }

    .team-info {
        margin-left: 0; /* Remove margin when stacked */
    }
}

@media (max-width: 480px) {
    .footer-nav {
        flex-direction: column; /* Stack nav columns */
        gap: 1.5rem;
    }

    .footer-nav-column {
        flex-basis: 100%; /* Full width */
    }

    .footer-donate-heading {
    }
}
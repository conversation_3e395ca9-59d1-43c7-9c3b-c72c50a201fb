# Friedensrat Website - Content Editing Guide

This guide explains how to edit content on your Ghost CMS website, particularly for the landing page.

## Accessing the Admin Panel

1. Go to `http://your-website.com/ghost/` (replace with your actual domain)
2. Log in with your username and password

## Basic Settings

### Site Title and Description

1. Go to **Settings** > **General**
2. Edit the **Title** and **Description** fields
3. Click **Save**

These will appear in the header section of your homepage.

### Navigation Menu

1. Go to **Settings** > **Navigation**
2. Add, edit, or remove menu items
3. You can create dropdown menus by adding items as children of other items
4. Click **Save**

## Editing Landing Page Content

The landing page consists of several sections that can be edited through the Ghost admin panel:

### Highlight Sections

The highlight sections on the landing page are created using Pages with specific tags:

#### Creating/Editing Highlight 1:

1. Go to **Pages** in the admin sidebar
2. Create a new page or edit the existing "Highlight 1" page
3. Set the title (e.g., "Journalismus der unabhängig bleibt, weltweit")
4. Add content in the editor
5. Add a featured image (this will appear on the right side of the highlight)
6. In the page settings (gear icon):
   - Set the slug to `homepage-highlight-1`
   - Add the tag `homepage-section`
7. Click **Publish** or **Update**

#### Creating/Editing Highlight 2:

1. Go to **Pages** in the admin sidebar
2. Create a new page or edit the existing "Highlight 2" page
3. Set the title (e.g., "Dialog und Begegnung für den Frieden")
4. Add content in the editor
5. Add a featured image (this will appear on the right side of the highlight)
6. In the page settings (gear icon):
   - Set the slug to `homepage-highlight-2`
   - Add the tag `homepage-section`
7. Click **Publish** or **Update**

### Team Section

The team section consists of a team section page and individual team member posts:

#### Creating/Editing the Team Section:

1. Go to **Pages** in the admin sidebar
2. Create a new page or edit the existing "Team Section" page
3. Set the title (e.g., "Unser Team")
4. Add content in the editor (this will appear as the quote in the team section)
   - Format: `<p>"Quote text"</p><p class="quote-author">Author Name</p>`
5. In the page settings (gear icon):
   - Set the slug to `homepage-team-section`
   - Add the tag `homepage-section`
6. Click **Publish** or **Update**

#### Creating/Editing Team Members for Homepage:

1. Go to **Posts** in the admin sidebar
2. Create a new post or edit an existing team member post
3. Set the title to the team member's name

## Articles Page Setup

### Creating the Articles Page

To set up the dedicated articles page:

1. **Create a new page**:
   - Go to "Pages" in the Ghost Admin
   - Click "New page"
   - Set the title to "Alle Artikel" or "Articles"
   - Set the page URL to `articles`
   - **Important**: In the page settings (gear icon), scroll down to "Custom Template" and select "custom-articles"
   - Add any introductory content if desired
   - Publish the page

2. **Add to Navigation**:
   - Go to "Settings" → "Navigation"
   - Add a link to `/articles/` with the label "Blog" or "Artikel"
   - Save changes

### How the Articles Page Works

The articles page automatically:
- Shows the latest post as a featured highlight at the top
- Displays all posts (except events) in a responsive grid
- Provides category filters based on your tags
- Includes pagination for large numbers of posts
- Adapts the header color based on the category being viewed

### Category Filtering

Users can filter articles by:
- **All**: Shows all posts
- **Tag-based filters**: Each tag becomes a filter option
- **Author pages**: Click on author names to see their posts
- **Tag pages**: Click on category tags to see posts in that category

### Article Categories

Use tags to categorize your articles:
- **artikel** (blue): General articles
- **internes** (teal): Internal news
- **weltgeschehen** (purple): World events
- **friedenszeitung** (orange): Peace newspaper
- **events** (green): Events (excluded from articles page)
- **technik** (violet): Technology
- **politik** (red): Politics
- **wirtschaft** (orange): Economy

Each category has its own color scheme that appears in the header and tags.
4. Add a featured image (this will be the team member's portrait)
5. In the post settings (gear icon):
   - Set the excerpt to the team member's title/position
   - Add the tag `homepage-team-member`
6. Click **Publish** or **Update**

The first three team members with the `homepage-team-member` tag will appear on the landing page.

### Events Section

Events for the homepage are created as posts with the `homepage-event` tag:

1. Go to **Posts** in the admin sidebar
2. Create a new post or edit an existing event post
3. Set the title to the event name
4. Add content describing the event
5. In the post settings (gear icon):
   - Add the tag `homepage-event`
   - Add another tag for the event type (e.g., `Diskussion`, `Konferenz`)
6. Click **Publish** or **Update**

The three most recent posts with the `homepage-event` tag will appear in the events section.

### Article Slider

The article slider automatically displays your most recent posts. To add a post that will appear in the slider:

1. Go to **Posts** in the admin sidebar
2. Create a new post
3. Add a title, content, and featured image
4. Add appropriate tags
5. Click **Publish**

## Tips for Content Creation

1. **Images**: Use high-quality images that are at least 1200px wide
2. **Text Formatting**: Use the editor's formatting options to create headings, lists, and links
3. **Tags**: Use tags consistently to categorize your content
4. **Featured Images**: Always add featured images to posts and pages for better visual appeal
5. **Excerpts**: Use excerpts to provide a brief summary of your content

## Need Help?

If you need assistance with editing your website, please contact your website administrator.
